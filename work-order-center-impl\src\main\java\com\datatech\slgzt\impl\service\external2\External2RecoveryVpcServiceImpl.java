package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import com.datatech.slgzt.service.external2.External2RecoveryResourceService;
import com.datatech.slgzt.service.network.VpcMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对公vpc回收
 */
@Service
@Slf4j
public class External2RecoveryVpcServiceImpl implements External2RecoveryResourceService {

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private VpcMessageService vpcMessageService;

    @Override
    public void recoveryResource(External2RecoveryOrderDTO dto, List<External2RecoveryOrderProductDTO> productDTOs) {
        for (External2RecoveryOrderProductDTO product : productDTOs) {
            if (ProductTypeEnum.VPC.getCode().equals(product.getProductType())) {
                String resourceDetailId = product.getResourceDetailId();
                vpcMessageService.vpcRecycle(resourceDetailId, 1L, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                productManager.updateStatusByIds(ListUtil.toList(product.getId()), RecoveryStatusEnum.RECOVERING.getType());
            }
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.VPC;
    }
}
