package com.datatech.slgzt.model.req.external2;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class External2OrderUpdateReq {

    @NotBlank(message = "ID不能为空")
    private String id;

    @NotBlank(message = "工单编号不能为空")
    private String orderCode;

    @NotNull(message = "创建人不能为空")
    private Long createBy;

    @NotBlank(message = "创建人姓名不能为空")
    private String createByName;
} 