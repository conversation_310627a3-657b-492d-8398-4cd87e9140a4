package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.External2RecoveryOrderProductManagerConvert;
import com.datatech.slgzt.dao.External2RecoveryOrderProductDAO;
import com.datatech.slgzt.dao.model.order.External2RecoveryOrderProductDO;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.External2RecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2回收工单产品Manager实现类
 */
@Service
public class External2RecoveryOrderProductManagerImpl implements External2RecoveryOrderProductManager {

    @Resource
    private External2RecoveryOrderProductDAO dao;

    @Resource
    private External2RecoveryOrderProductManagerConvert convert;


    @Override
    public void insert(External2RecoveryOrderProductDTO productDTO) {
        dao.insert(convert.dto2do(productDTO));
    }

    @Override
    public External2RecoveryOrderProductDTO getById(Long id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public void update(External2RecoveryOrderProductDTO productDTO) {
        dao.update(convert.dto2do(productDTO));
    }


    @Override
    public List<External2RecoveryOrderProductDTO> listByWorkOrderId(String workOrderId) {
        return StreamUtils.mapArray(dao.listByWorkOrderId(workOrderId), convert::do2dto);
    }

    @Override
    public List<External2RecoveryOrderProductDTO> listByResourceDetailId(String resourceDetailId, RecoveryStatusEnum recoveryStatus) {
        return StreamUtils.mapArray(dao.listByResourceDetailId(resourceDetailId, recoveryStatus.getType()), convert::do2dto);
    }

    @Override
    public List<External2RecoveryOrderProductDTO> listChildren(Long id) {
        return StreamUtils.mapArray(dao.listChildren(id), convert::do2dto);
    }

    @Override
    public void updateStatusByParentId(Long id, Integer status) {
        External2RecoveryOrderProductDO recoveryWorkOrderProductDO = new External2RecoveryOrderProductDO();
        recoveryWorkOrderProductDO.setParentProductId(id);
        recoveryWorkOrderProductDO.setRecoveryStatus(status);
        dao.updateByParentId(recoveryWorkOrderProductDO);
    }

    @Override
    public External2RecoveryOrderProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public List<External2RecoveryOrderProductDTO> getByIds(List<Long> ids) {
        return StreamUtils.mapArray(dao.getByIds(ids), convert::do2dto);
    }

    @Override
    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        dao.updateHcmByCmdbIds(configIds, status);
    }

    @Override
    public void updateHcmByIds(List<Long> ids, String status) {
        dao.updateHcmByIds(ids, status);
    }


    @Override
    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        dao.updateTenantConfirmByIds(ids, tenantConfirm);
    }

    @Override
    public void deleteByWorkOrderId(String workOrderId) {
        dao.deleteByWorkOrderId(workOrderId);
    }


    public External2RecoveryOrderProductDTO getByCmdbId(String cmdbId) {
        return convert.do2dto(dao.getByCmdbId(cmdbId));
    }

    @Override
    public void updateStatusByIds(List<Long> ids, Integer status) {
        dao.updateStatusByIds(ids, status);
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }
}
