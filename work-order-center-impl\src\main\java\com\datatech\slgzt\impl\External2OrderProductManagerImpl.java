package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.External2OrderProductManagerConvert;
import com.datatech.slgzt.dao.External2OrderProductDAO;
import com.datatech.slgzt.dao.model.order.External2OrderProductDO;
import com.datatech.slgzt.manager.External2OrderProductManager;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class External2OrderProductManagerImpl implements External2OrderProductManager {

    @Resource
    private External2OrderProductDAO external2OrderProductDAO;

    @Resource
    private External2OrderProductManagerConvert external2OrderProductManagerConvert;

    @Override
    public List<External2OrderProductDTO> list(External2OrderProductQuery query) {
        List<External2OrderProductDO> list = external2OrderProductDAO.list(query);
        return external2OrderProductManagerConvert.dos2DTOs(list);
    }

    @Override
    public PageInfo<External2OrderProductDTO> page(External2OrderProductQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<External2OrderProductDO> list = external2OrderProductDAO.list(query);
        return new PageInfo<>(external2OrderProductManagerConvert.dos2DTOs(list));
    }

    @Override
    public void insert(External2OrderProductDTO dto) {
        External2OrderProductDO external2OrderProductDO = external2OrderProductManagerConvert.dto2do(dto);
        external2OrderProductDAO.insert(external2OrderProductDO);
    }

    @Override
    public void update(External2OrderProductDTO dto) {
        External2OrderProductDO external2OrderProductDO = external2OrderProductManagerConvert.dto2do(dto);
        external2OrderProductDAO.updateById(external2OrderProductDO);
    }

    @Override
    public void delete(Long id) {
        external2OrderProductDAO.delete(id);
    }

    @Override
    public void deleteByWorkOrderId(String workOrderId) {
        external2OrderProductDAO.deleteByWorkOrderId(workOrderId);
    }

    @Override
    public External2OrderProductDTO getById(Long id) {
        External2OrderProductDO external2OrderProductDO = external2OrderProductDAO.getById(id);
        return external2OrderProductManagerConvert.do2dto(external2OrderProductDO);
    }

    @Override
    public External2OrderProductDTO getByGid(String gid) {
        External2OrderProductDO external2OrderProductDO = external2OrderProductDAO.getByGid(gid);
        return external2OrderProductManagerConvert.do2dto(external2OrderProductDO);
    }

    @Override
    public External2OrderProductDTO getBySubOrderId(Long subOrderId) {
        External2OrderProductDO external2OrderProductDO = external2OrderProductDAO.getBySubOrderId(subOrderId);
        return external2OrderProductManagerConvert.do2dto(external2OrderProductDO);
    }

    @Override
    public void updateStatusById(Long id, String status) {
        External2OrderProductDO updateDO = new External2OrderProductDO();
        updateDO.setId(id);
        updateDO.setOpenStatus(status);
        external2OrderProductDAO.updateById(updateDO);
    }

    @Override
    public void updateStatusByParentId(Long parentId, String status) {
        External2OrderProductDO updateDO = new External2OrderProductDO();
        updateDO.setParentProductId(parentId);
        updateDO.setOpenStatus(status);
        external2OrderProductDAO.updateByParentId(updateDO);
    }
}