package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface External2OrderProductManager {

    List<External2OrderProductDTO> list(External2OrderProductQuery query);

    PageInfo<External2OrderProductDTO> page(External2OrderProductQuery query);

    void insert(External2OrderProductDTO dto);

    void update(External2OrderProductDTO dto);

    void delete(Long id);

    void deleteByWorkOrderId(String workOrderId);

    External2OrderProductDTO getById(Long id);

    External2OrderProductDTO getByGid(String gid);

    External2OrderProductDTO getBySubOrderId(Long subOrderId);

    void updateStatusById(Long id, String status);

    void updateStatusByParentId(Long parentId, String status);
}
