package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2回收工单产品DTO
 */
@Data
public class External2RecoveryOrderProductDTO {

    private Long id;

    //工单Id
    private String workOrderId;

    /**
     * 产品类型
     * esc
     * gsc
     * eip 等
     */
    private String productType;


    //属性快照
    private String propertySnapshot;

    //父类产品id 可以为空
    private Long parentProductId;

    /**
     * gid
     */
    private String gid;

    /**
     * gid
     */
    private Long subOrderId;

    //退维
    private String hcmStatus;

    //消息
    private String message;

    //回收状态
    private Integer recoveryStatus;


    private String ext;

    //资源详情id
    private String resourceDetailId;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

    /**
     * cmdbId
     */
    private String cmdbId;

    private Boolean tenantConfirm;

    /**
     * 是否需要同步回收
     */
    private Boolean syncRecovery;
}
