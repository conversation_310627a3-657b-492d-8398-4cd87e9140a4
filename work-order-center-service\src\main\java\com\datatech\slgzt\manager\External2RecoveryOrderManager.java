package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.query.External2RecoveryOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface External2RecoveryOrderManager {

    String createWorkOrder(External2RecoveryOrderDTO recoveryWorkOrderDTO);

    PageResult<External2RecoveryOrderDTO> page(External2RecoveryOrderQuery query);

    External2RecoveryOrderDTO getById(String id);

    void update(External2RecoveryOrderDTO orderDTO);

    List<External2RecoveryOrderDTO> list(External2RecoveryOrderQuery query);
}
