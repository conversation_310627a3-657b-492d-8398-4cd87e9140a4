package com.datatech.slgzt.model.req.external2.change;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月03日 19:02:28
 */
@Data
public class External2ChangeOrderPageReq {
    private Integer pageNum = 1;

    private Integer pageSize = 10;

    //工单编码
    private String orderCode;


    /**
     * 创建人名称
     */
    @JsonProperty("createdUserName")
    private String creator;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;
}
