package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2变更工单产品表
 */
@Data
@TableName("WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT")
public class External2ChangeOrderProductDO {

    //产品id
    @TableField("ID")
    private Long id;

    //工单Id
    @TableField("WORK_ORDER_ID")
    private String workOrderId;

    /**
     * 创建的工单id
     * 如果只是更变工单这个字段会是空的
     */
    @TableField("CREATE_WORK_ORDER_ID")
    private String createWorkOrderId;

    /**
     * 更变类型
     */
    @TableField("CHANGE_TYPE")
    private String changeType;

    /**
     * 产品类型
     * esc
     * gsc
     * eip 等
     */
    @TableField("PRODUCT_TYPE")
    private String productType;


    //属性快照
    @TableField("PROPERTY_SNAPSHOT")
    private String propertySnapshot;

    //父类产品id 可以为空
    @TableField("PARENT_PRODUCT_ID")
    private Long parentProductId;

    /**
     * gid
     */
    @TableField("GID")
    private String gid;

    /**
     * gid
     */
    @TableField("SUB_ORDER_ID")
    private Long subOrderId;

    //开通状态
    @TableField("CHANGE_STATUS")
    private String changeStatus;

    //消息
    @TableField("MESSAGE")
    private String message;

    @TableLogic(value = "1", delval = "0")
    @TableField("ENABLED")
    private Boolean enabled;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    //resourceDetailId
    @TableField("RESOURCE_DETAIL_ID")
    private String resourceDetailId;

    //屏蔽告警
    @TableField("BLOCK_WARNING")
    private Boolean blockWarning;

    //租户确认
    @TableField("TENANT_CONFIRM")
    private Boolean tenantConfirm;

    /**
     * 串行变更状态：0-不需要串行变更，1-需要串行变更(但未变更)，2-串行变更待变更，3-串行变更过
     */
    @TableField("SERIAL_CHANGE_STATUS")
    private Integer serialChangeStatus;
}
