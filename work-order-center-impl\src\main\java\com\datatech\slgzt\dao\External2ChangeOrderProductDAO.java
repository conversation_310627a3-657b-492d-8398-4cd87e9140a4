package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2ChangeOrderProductMapper;
import com.datatech.slgzt.dao.model.order.External2ChangeOrderProductDO;
import com.datatech.slgzt.model.query.External2ChangeOrderProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2变更工单产品DAO
 */
@Repository
public class External2ChangeOrderProductDAO {

    @Resource
    private External2ChangeOrderProductMapper external2ChangeOrderProductMapper;

    public void insert(External2ChangeOrderProductDO external2ChangeOrderProductDO) {
        external2ChangeOrderProductMapper.insert(external2ChangeOrderProductDO);
    }

    public void update(External2ChangeOrderProductDO external2ChangeOrderProductDO) {
        external2ChangeOrderProductMapper.updateById(external2ChangeOrderProductDO);
    }

    public void delete(Long id) {
        external2ChangeOrderProductMapper.deleteById(id);
    }

    public External2ChangeOrderProductDO getById(Long id) {
        return external2ChangeOrderProductMapper.selectById(id);
    }

    public List<External2ChangeOrderProductDO> list(External2ChangeOrderProductQuery query) {
        return external2ChangeOrderProductMapper.selectList(
                Wrappers.<External2ChangeOrderProductDO>lambdaQuery()
                        .eq(ObjNullUtils.isNotNull(query.getWorkOrderId()), External2ChangeOrderProductDO::getWorkOrderId, query.getWorkOrderId())
                        .eq(StringUtils.isNotBlank(query.getCreateWorkOrderId()), External2ChangeOrderProductDO::getCreateWorkOrderId, query.getCreateWorkOrderId())
                        .eq(StringUtils.isNotBlank(query.getChangeType()), External2ChangeOrderProductDO::getChangeType, query.getChangeType())
                        .eq(StringUtils.isNotBlank(query.getProductType()), External2ChangeOrderProductDO::getProductType, query.getProductType())
                        .eq(ObjNullUtils.isNotNull(query.getParentProductId()), External2ChangeOrderProductDO::getParentProductId, query.getParentProductId())
                        .eq(StringUtils.isNotBlank(query.getGid()), External2ChangeOrderProductDO::getGid, query.getGid())
                        .eq(ObjNullUtils.isNotNull(query.getSubOrderId()), External2ChangeOrderProductDO::getSubOrderId, query.getSubOrderId())
                        .eq(StringUtils.isNotBlank(query.getChangeStatus()), External2ChangeOrderProductDO::getChangeStatus, query.getChangeStatus())
                        .eq(StringUtils.isNotBlank(query.getResourceDetailId()), External2ChangeOrderProductDO::getResourceDetailId, query.getResourceDetailId())
                        .eq(ObjNullUtils.isNotNull(query.getBlockWarning()), External2ChangeOrderProductDO::getBlockWarning, query.getBlockWarning())
                        .eq(ObjNullUtils.isNotNull(query.getTenantConfirm()), External2ChangeOrderProductDO::getTenantConfirm, query.getTenantConfirm())
                        .eq(ObjNullUtils.isNotNull(query.getSerialChangeStatus()), External2ChangeOrderProductDO::getSerialChangeStatus, query.getSerialChangeStatus())
                        .in(ObjNullUtils.isNotNull(query.getIdList()), External2ChangeOrderProductDO::getId, query.getIdList())
                        .in(ObjNullUtils.isNotNull(query.getWorkOrderIdList()), External2ChangeOrderProductDO::getWorkOrderId, query.getWorkOrderIdList())
                        .in(ObjNullUtils.isNotNull(query.getSubOrderIds()), External2ChangeOrderProductDO::getSubOrderId, query.getSubOrderIds())
                        .between(
                                ObjNullUtils.isNotNull(query.getCreateTimeStart()) && ObjNullUtils.isNotNull(query.getCreateTimeEnd()),
                                External2ChangeOrderProductDO::getCreateTime,
                                query.getCreateTimeStart(),
                                query.getCreateTimeEnd()
                        )
                        .between(
                                ObjNullUtils.isNotNull(query.getModifyTimeStart()) && ObjNullUtils.isNotNull(query.getModifyTimeEnd()),
                                External2ChangeOrderProductDO::getModifyTime,
                                query.getModifyTimeStart(),
                                query.getModifyTimeEnd()
                        )
                        .orderByDesc(External2ChangeOrderProductDO::getCreateTime)
        );
    }

    public void updateStatusByIds(List<Long> ids, String status) {
        External2ChangeOrderProductDO changeWorkOrderProductDO = new External2ChangeOrderProductDO();
        changeWorkOrderProductDO.setChangeStatus(status);
        external2ChangeOrderProductMapper.update(changeWorkOrderProductDO, Wrappers.<External2ChangeOrderProductDO>lambdaQuery().in(External2ChangeOrderProductDO::getId, ids));
    }

    public External2ChangeOrderProductDO getBySubOrderId(Long subOrderId) {
        return external2ChangeOrderProductMapper.selectOne(Wrappers.<External2ChangeOrderProductDO>lambdaQuery()
                .eq(External2ChangeOrderProductDO::getSubOrderId, subOrderId));
    }
}
