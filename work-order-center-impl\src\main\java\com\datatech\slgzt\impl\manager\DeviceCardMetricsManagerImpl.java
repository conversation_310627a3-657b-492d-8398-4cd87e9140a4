package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.BaseMetricInfoConver;
import com.datatech.slgzt.dao.DeviceCardMetricsDAO;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;
import com.datatech.slgzt.manager.DeviceCardMetricsManager;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 显卡监控指标
 */
@Service
public class DeviceCardMetricsManagerImpl  implements DeviceCardMetricsManager {
    @Resource
    private DeviceCardMetricsDAO deviceCardMetricsDAO;
    @Resource
    private BaseMetricInfoConver baseMetricInfoConver;

    @Override
    public void create(DeviceCardMetricsDTO dto) {
        DeviceCardMetricsDO deviceCardMetricsDO = baseMetricInfoConver.dto2do(dto);
        deviceCardMetricsDO.setCreatedAt(LocalDateTime.now());
        deviceCardMetricsDAO.insert(deviceCardMetricsDO);

    }

    @Override
    public void saveBatch(List<DeviceCardMetricsDTO> deviceCardMetricsDTOS) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOS = StreamUtils.mapArray(deviceCardMetricsDTOS, baseMetricInfoConver::dto2do);
        deviceCardMetricsDOS.forEach(deviceCardMetricsDO -> deviceCardMetricsDO.setCreatedAt(LocalDateTime.now()));
        deviceCardMetricsDAO.insertBatch(deviceCardMetricsDOS);
    }

    @Override
    public void delByGpuTime(String gpuTime) {
         deviceCardMetricsDAO.delByDataTime(gpuTime);
    }

    @Override
    public void update(DeviceCardMetricsDTO dto) {
        DeviceCardMetricsDO deviceCardMetricsDO = baseMetricInfoConver.dto2do(dto);
        deviceCardMetricsDAO.updateById(deviceCardMetricsDO);
    }

    @Override
    public void delete(Long id) {
        deviceCardMetricsDAO.deleteById(id);
    }
    @Override
    public void deleteBatch(List<Long> ids) {
        deviceCardMetricsDAO.deleteByIds(ids);
    }



    @Override
    public List<DeviceCardMetricsDTO> selectGpuMetricsDTO(String gpuTime) {
        //查询指标
        return StreamUtils.mapArray(deviceCardMetricsDAO.selectDeviceMetric(gpuTime),baseMetricInfoConver::do2dto);
    }

    @Override
    public List<DeviceCardMetricsDTO> selectGpuMetricsDTO(DeviceMetricQuery deviceMetricQuery) {
        return StreamUtils.mapArray(deviceCardMetricsDAO.selectDeviceMetric(deviceMetricQuery),baseMetricInfoConver::do2dto);
    }

    @Override
    public List<DeviceCardMetricsDTO> queryAvgDeviceMetrics(DeviceMetricQuery deviceMetricQuery) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOList = deviceCardMetricsDAO.queryAvgDeviceMetrics(deviceMetricQuery);
        return StreamUtils.mapArray(deviceCardMetricsDOList,baseMetricInfoConver::do2dto);
    }

    @Override
    public List<DeviceCardMetricsDTO> queryGpuMetricsAggregated(List<String> deviceIds, DeviceMetricQuery deviceMetricQuery) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOList = deviceCardMetricsDAO.queryGpuMetricsAggregated(deviceIds, deviceMetricQuery);
        return StreamUtils.mapArray(deviceCardMetricsDOList, baseMetricInfoConver::do2dto);
    }

    @Override
    public List<DeviceCardMetricsDTO> queryCardUtilizationMetrics(String areaCode, String modelName,
                                                                java.time.LocalDateTime startTime, 
                                                                java.time.LocalDateTime endTime,
                                                                String aggregationType) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOList = deviceCardMetricsDAO.queryCardUtilizationMetrics(
                areaCode, modelName, startTime, endTime, aggregationType);
        return StreamUtils.mapArray(deviceCardMetricsDOList, baseMetricInfoConver::do2dto);
    }



    @Override
    public List<DeviceCardMetricsDTO> queryCardUtilizationMetrics2(String areaCode, String modelName,
                                                                  java.time.LocalDateTime startTime,
                                                                  java.time.LocalDateTime endTime,
                                                                  String aggregationType) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOList = deviceCardMetricsDAO.queryCardUtilizationMetrics2(
                areaCode, modelName, startTime, endTime, aggregationType);
        return StreamUtils.mapArray(deviceCardMetricsDOList, baseMetricInfoConver::do2dto);
    }

    @Override
    public List<DeviceCardMetricsDTO> queryBusinessSystemHistoryMetrics(String areaCode, String modelName,
                                                                       java.time.LocalDateTime startTime,
                                                                       java.time.LocalDateTime endTime) {
        List<DeviceCardMetricsDO> deviceCardMetricsDOList = deviceCardMetricsDAO.queryBusinessSystemHistoryMetrics(
                areaCode, modelName, startTime, endTime);
        return StreamUtils.mapArray(deviceCardMetricsDOList, baseMetricInfoConver::do2dto);
    }
}
