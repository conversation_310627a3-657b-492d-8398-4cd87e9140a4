package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部2变更工单查询对象
 */
@Data
public class External2ChangeOrderQuery {

    /** 订单编号 */
    private String orderCode;

    /** 租户ID */
    private Long tenantId;

    /** 租户名称 */
    private String tenantName;

    /** 创建人 */
    private String creator;

    /** 创建人ID */
    private Long creatorId;

    /** Job执行ID */
    private Long jobExecutionId;

    /** ID列表 */
    private List<String> idList;

    /** 创建时间开始 */
    private LocalDateTime createTimeStart;

    /** 创建时间结束 */
    private LocalDateTime createTimeEnd;

    /** 页码 */
    private Integer pageNum = 1;

    /** 每页大小 */
    private Integer pageSize = 10;

    private List<Long> tenantIds;
}
