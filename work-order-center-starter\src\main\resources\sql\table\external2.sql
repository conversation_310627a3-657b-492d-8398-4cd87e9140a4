-- 外部2工单相关表结构

-- SLGZT.WOC_EXTERNAL2_ORDER definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_ORDER (
    ID VARCHAR2(32) NOT NULL,
    ORDER_TITLE VARCHAR2(255) NULL,
    ORDER_CODE VARCHAR2(64) NULL,
    ORDER_TYPE VARCHAR2(32) NULL,
    BILL_ID VARCHAR2(64) NULL,
    CUSTOM_NO VARCHAR2(64) NULL,
    ORDER_AMOUNT NUMBER(10,2) DEFAULT 0 NULL,
    ORDER_STATUS VARCHAR2(32) NULL,
    BUSINESS_SYSTEM_ID NUMBER(19,0) NULL,
    BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
    TENANT_ID NUMBER(19,0) NULL,
    TENANT_NAME VARCHAR2(128) NULL,
    CREATE_BY NUMBER(19,0) NULL,
    CREATE_BY_NAME VARCHAR2(64) NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_BY NUMBER(19,0) NULL,
    UPDATE_BY_NAME VARCHAR2(64) NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_ORDER PRIMARY KEY (ID)
);

-- SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT (
    ID NUMBER(19,0) NOT NULL,
    ORDER_ID VARCHAR2(32) NOT NULL,
    PRODUCT_TYPE VARCHAR2(50) NOT NULL,
    PROPERTY_SNAPSHOT TEXT NULL,
    PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
    SUB_ORDER_ID NUMBER(19,0) NULL,
    GID VARCHAR2(64) NULL,
    OPEN_STATUS VARCHAR2(32) NULL,
    ENABLED NUMBER(1,0) DEFAULT 1 NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_ORDER_PRODUCT PRIMARY KEY (ID)
);

-- SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER (
    ID VARCHAR2(32) NOT NULL,
    RECOVERY_TYPE VARCHAR2(32) NULL,
    ORDER_TITLE VARCHAR2(255) NULL,
    ORDER_CODE VARCHAR2(64) NULL,
    ORDER_STATUS VARCHAR2(32) NULL,
    BILL_ID VARCHAR2(64) NULL,
    TENANT_ID NUMBER(19,0) NULL,
    TENANT_NAME VARCHAR2(128) NULL,
    CREATE_BY NUMBER(19,0) NULL,
    CREATE_BY_NAME VARCHAR2(64) NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_BY NUMBER(19,0) NULL,
    UPDATE_BY_NAME VARCHAR2(64) NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_RECOVERY_ORDER PRIMARY KEY (ID)
);

-- SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT (
    ID NUMBER(19,0) NOT NULL,
    WORK_ORDER_ID VARCHAR2(32) NOT NULL,
    PRODUCT_TYPE VARCHAR2(50) NOT NULL,
    PROPERTY_SNAPSHOT TEXT NULL,
    PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
    SUB_ORDER_ID NUMBER(19,0) NULL,
    GID VARCHAR2(64) NULL,
    RECOVERY_STATUS VARCHAR2(32) NULL,
    ENABLED NUMBER(1,0) DEFAULT 1 NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT PRIMARY KEY (ID)
);

-- SLGZT.WOC_EXTERNAL2_CHANGE_ORDER definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_CHANGE_ORDER (
    ID VARCHAR2(32) NOT NULL,
    ORDER_TYPE VARCHAR2(32) NULL,
    ORDER_TITLE VARCHAR2(255) NULL,
    ORDER_CODE VARCHAR2(64) NULL,
    ORDER_STATUS VARCHAR2(32) NULL,
    BILL_ID VARCHAR2(64) NULL,
    CUSTOM_NO VARCHAR2(64) NULL,
    BUSINESS_SYSTEM_CODE VARCHAR2(64) NULL,
    TENANT_ID NUMBER(19,0) NULL,
    TENANT_NAME VARCHAR2(128) NULL,
    CREATE_BY NUMBER(19,0) NULL,
    CREATE_BY_NAME VARCHAR2(64) NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_BY NUMBER(19,0) NULL,
    UPDATE_BY_NAME VARCHAR2(64) NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_CHANGE_ORDER PRIMARY KEY (ID)
);

-- SLGZT.WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT definition
CREATE TABLE SLGZT.WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT (
    ID NUMBER(19,0) NOT NULL,
    WORK_ORDER_ID VARCHAR2(32) NOT NULL,
    CREATE_WORK_ORDER_ID VARCHAR2(32) NULL,
    CHANGE_TYPE VARCHAR2(50) NOT NULL,
    PRODUCT_TYPE VARCHAR2(50) NOT NULL,
    PROPERTY_SNAPSHOT TEXT NULL,
    PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
    SUB_ORDER_ID NUMBER(19,0) NULL,
    GID VARCHAR2(64) NULL,
    CHANGE_STATUS VARCHAR2(32) NULL,
    ENABLED NUMBER(1,0) DEFAULT 1 NULL,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_CODE ON SLGZT.WOC_EXTERNAL2_ORDER (ORDER_CODE);
CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_TENANT_ID ON SLGZT.WOC_EXTERNAL2_ORDER (TENANT_ID);
CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_BUSINESS_SYSTEM_ID ON SLGZT.WOC_EXTERNAL2_ORDER (BUSINESS_SYSTEM_ID);

CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_PRODUCT_ORDER_ID ON SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT (ORDER_ID);
CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_PRODUCT_GID ON SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT (GID);
CREATE INDEX IDX_WOC_EXTERNAL2_ORDER_PRODUCT_SUB_ORDER_ID ON SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT (SUB_ORDER_ID);

CREATE INDEX IDX_WOC_EXTERNAL2_RECOVERY_ORDER_CODE ON SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER (ORDER_CODE);
CREATE INDEX IDX_WOC_EXTERNAL2_RECOVERY_ORDER_TENANT_ID ON SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER (TENANT_ID);

CREATE INDEX IDX_WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT_WORK_ORDER_ID ON SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT (WORK_ORDER_ID);
CREATE INDEX IDX_WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT_GID ON SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT (GID);

CREATE INDEX IDX_WOC_EXTERNAL2_CHANGE_ORDER_CODE ON SLGZT.WOC_EXTERNAL2_CHANGE_ORDER (ORDER_CODE);
CREATE INDEX IDX_WOC_EXTERNAL2_CHANGE_ORDER_TENANT_ID ON SLGZT.WOC_EXTERNAL2_CHANGE_ORDER (TENANT_ID);

CREATE INDEX IDX_WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT_WORK_ORDER_ID ON SLGZT.WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT (WORK_ORDER_ID);
CREATE INDEX IDX_WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT_GID ON SLGZT.WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT (GID);

-- 添加注释
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_ORDER IS '外部2工单表';
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_ORDER_PRODUCT IS '外部2工单产品表';
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER IS '外部2回收工单表';
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_RECOVERY_ORDER_PRODUCT IS '外部2回收工单产品表';
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_CHANGE_ORDER IS '外部2变更工单表';
COMMENT ON TABLE SLGZT.WOC_EXTERNAL2_CHANGE_ORDER_PRODUCT IS '外部2变更工单产品表';
