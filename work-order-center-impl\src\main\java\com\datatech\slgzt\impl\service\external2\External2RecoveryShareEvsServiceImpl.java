package com.datatech.slgzt.impl.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 共享云硬盘回收服务实现类
 * <AUTHOR>
 * @description 共享云硬盘回收逻辑，继承自EVS云硬盘回收服务，但需要处理共享盘的特殊属性
 * @date 2025年 09月17日
 */
@Service
@Slf4j
public class External2RecoveryShareEvsServiceImpl extends External2RecoveryEvsServiceImpl {

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.SHARE_EVS;
    }
}
