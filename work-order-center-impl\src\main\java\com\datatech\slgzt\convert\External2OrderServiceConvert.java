package com.datatech.slgzt.convert;

import cn.hutool.core.util.NumberUtil;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.External2OrderCreateOpm;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;

import java.math.BigDecimal;
import java.util.List;

@Mapper(componentModel = "spring")
public interface External2OrderServiceConvert {

    default void fillBase(External2OrderCreateOpm orderCreateOpm) {
        //获取ecs对象
        List<CloudEcsResourceModel> ecsModelList = orderCreateOpm.getEcsModelList();
        //获取rdsMysql对象
        List<MysqlV2Model> rdsMysqlModelList = orderCreateOpm.getRdsMysqlModelList();
        //获取redis对象
        List<EcsModel> redisModelList = orderCreateOpm.getRedisModelList();
        //获取postgreSql对象
        List<EcsModel> postgreSqlModelList = orderCreateOpm.getPostgreSqlModelList();
        //获取mysql对象
        List<EcsModel> mysqlModelList = orderCreateOpm.getMysqlModelList();
        //获取gcs对象
        List<CpuEcsResourceModel> gcsModelList = orderCreateOpm.getGcsModelList();
        //获取evs对象
        List<EvsModel> evsModelList = orderCreateOpm.getEvsModelList();
        //获取eip对象
        List<EipModel> eipModelList = orderCreateOpm.getEipModelList();
        //obs对象
        List<ObsModel> obsModelList = orderCreateOpm.getObsModelList();
        //slb对象
        List<SlbModel> slbModelList = orderCreateOpm.getSlbModelList();
        //nat对象
        List<NatGatwayModel> natModelList = orderCreateOpm.getNatModelList();
        //cloudPort
        List<CloudPortModel> cloudPortModelList = orderCreateOpm.getCloudPortModelList();
        //vpn
        List<VpnModel> vpnModelList = orderCreateOpm.getVpnModelList();
        // backupModelList
        List<BackupModel> backupModelList = orderCreateOpm.getBackupModelList();

        opmBase(ecsModelList, orderCreateOpm);
        opmBase(rdsMysqlModelList, orderCreateOpm);
        opmBase(redisModelList, orderCreateOpm);
        opmBase(postgreSqlModelList, orderCreateOpm);
        opmBase(mysqlModelList, orderCreateOpm);
        opmBase(gcsModelList, orderCreateOpm);
        opmBase(evsModelList, orderCreateOpm);
        opmBase(eipModelList, orderCreateOpm);
        opmBase(obsModelList, orderCreateOpm);
        opmBase(slbModelList, orderCreateOpm);
        opmBase(natModelList, orderCreateOpm);
        opmBase(cloudPortModelList, orderCreateOpm);
        opmBase(vpnModelList, orderCreateOpm);
        opmBase(backupModelList, orderCreateOpm);

        orderCreateOpm.setEcsModelList(ecsModelList);
        orderCreateOpm.setRdsMysqlModelList(rdsMysqlModelList);
        orderCreateOpm.setRedisModelList(redisModelList);
        orderCreateOpm.setPostgreSqlModelList(postgreSqlModelList);
        orderCreateOpm.setMysqlModelList(mysqlModelList);
        orderCreateOpm.setGcsModelList(gcsModelList);
        orderCreateOpm.setEvsModelList(evsModelList);
        orderCreateOpm.setEipModelList(eipModelList);
        orderCreateOpm.setObsModelList(obsModelList);
        orderCreateOpm.setSlbModelList(slbModelList);
        orderCreateOpm.setNatModelList(natModelList);
        orderCreateOpm.setCloudPortModelList(cloudPortModelList);
        orderCreateOpm.setVpnModelList(vpnModelList);
        orderCreateOpm.setBackupModelList(backupModelList);
    }

    default <T extends BaseProductModel> void opmBase(List<T> modelList, External2OrderCreateOpm opm) {
        if (ObjNullUtils.isNull(modelList)) {
            return;
        }
        for (BaseProductModel model : modelList) {
            model.setTenantId(opm.getTenantId());
            model.setCustomNo(opm.getCustomNo());
            model.setBillId(opm.getBillId());
            model.setBusinessSystemId(opm.getBusinessSystemId());
            model.setBusinessSystemName(opm.getBusinessSystemName());
            calculateTotal(model, opm);
        }
    }

    /**
     * 计算金额
     * @param opm
     */
    default <T extends BaseProductModel> void calculateTotal(BaseProductModel model, External2OrderCreateOpm opm) {
        //获取ecs对象
        BigDecimal amount = opm.getAmount();
        BigDecimal amount1 = model.getAmount();
        opm.setAmount(NumberUtil.add(amount, amount1));
    }


    default ProductGeneralCheckOpm convertCheck(External2OrderCreateOpm opm) {
        ProductGeneralCheckOpm productGeneralCheckOpm = new ProductGeneralCheckOpm();
        //获取ecs对象
        List<CloudEcsResourceModel> ecsModelList = opm.getEcsModelList();
        //获取rdsMysql对象
        List<MysqlV2Model> rdsMysqlModelList = opm.getRdsMysqlModelList();
        //获取redis对象
        List<EcsModel> redisModelList = opm.getRedisModelList();
        //获取postgreSql对象
        List<EcsModel> postgreSqlModelList = opm.getPostgreSqlModelList();
        //获取mysql对象
        List<EcsModel> mysqlModelList = opm.getMysqlModelList();
        //获取gcs对象
        List<CpuEcsResourceModel> gcsModelList = opm.getGcsModelList();
        //获取evs对象
        List<EvsModel> evsModelList = opm.getEvsModelList();
        //获取eip对象
        List<EipModel> eipModelList = opm.getEipModelList();
        //obs对象
        List<ObsModel> obsModelList = opm.getObsModelList();
        //slb对象
        List<SlbModel> slbModelList = opm.getSlbModelList();
        //nat对象
        List<NatGatwayModel> natModelList = opm.getNatModelList();
        productGeneralCheckOpm.setEcsModelList(ecsModelList);
        productGeneralCheckOpm.setMysqlV2ModelList(rdsMysqlModelList);
        productGeneralCheckOpm.setRedisModelList(redisModelList);
        productGeneralCheckOpm.setPostgreSqlModelList(postgreSqlModelList);
        productGeneralCheckOpm.setMysqlModelList(mysqlModelList);
        productGeneralCheckOpm.setGcsModelList(gcsModelList);
        productGeneralCheckOpm.setEvsModelList(evsModelList);
        productGeneralCheckOpm.setEipModelList(eipModelList);
        productGeneralCheckOpm.setObsModelList(obsModelList);
        productGeneralCheckOpm.setSlbModelList(slbModelList);
        productGeneralCheckOpm.setNatModelList(natModelList);
        return productGeneralCheckOpm;
    }
}
