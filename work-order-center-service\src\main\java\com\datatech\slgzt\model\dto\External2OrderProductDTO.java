package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2工单产品DTO
 */
@Data
public class External2OrderProductDTO {

    private Long id;

    //工单Id
    private String orderId;

    /**
     * 产品类型
     * GoodsTypeEnum
     */
    private String productType;


    //属性快照
    private String propertySnapshot;

    //父类产品id 可以为空
    private Long parentProductId;

    //开通状态
    private String openStatus;

    //消息
    private String message;

    private String gid;

    private String ext;
    private Long subOrderId;

    //Job执行ID
    private Long jobExecutionId;

    private Boolean enabled;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

}
