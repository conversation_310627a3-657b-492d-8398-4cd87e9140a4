package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class WorkOrderTopicConsumer {
    private final static String WORK_ORDER_TOPIC = "prod_oac_resource_detail_topic";

    @Resource
    private StandardResourceDetailConsumer standardResourceDetailConsumer;
    @Resource
    private NonStanderResourceDetailConsumer nonStanderResourceDetailConsumer;
    @Resource
    private ChangeResourceDetailConsumer changeResourceDetailConsumer;
    @Resource
    private DgChangeResourceDetailConsumer dgChangeResourceDetailConsumer;
    @Resource
    private CorporateResourceDetailConsumer corporateResourceDetailConsumer;
    @Resource
    private DagResourceDetailConsumer dagResourceDetailConsumer;
    @Resource
    private External2ResourceDetailConsumer externalResourceDetailConsumer;
    @Resource
    private External2ChangeResourceDetailConsumer external2ChangeResourceDetailConsumer;

    @KafkaListener(groupId = "prod-work-order-resource-detail-group-standard", topics = {WORK_ORDER_TOPIC})
    public void consumeResourceMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("监听任务消息: {}", consumerRecordList.size());
        //转换消息放入集合中
        List<ResourceDetailDTO> subscribeList = new ArrayList<>();
        List<ResourceDetailDTO> nonStandardList = new ArrayList<>();
        List<ResourceDetailDTO> changeList = new ArrayList<>();
        List<ResourceDetailDTO> dgChangeList = new ArrayList<>();
        List<ResourceDetailDTO> externalList = new ArrayList<>();
        List<ResourceDetailDTO> corporateList = new ArrayList<>();
        List<ResourceDetailDTO> dagList = new ArrayList<>();
        List<ResourceDetailDTO> external2List = new ArrayList<>();
        List<ResourceDetailDTO> external2ChangeList = new ArrayList<>();
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            ResourceDetailDTO resourceDetail = JSONObject.parseObject(record.value(), ResourceDetailDTO.class);
            log.info("消息体解析：{}", resourceDetail);
            if (OrderTypeEnum.SUBSCRIBE.getCode().equals(resourceDetail.getSourceExtType())) {
                subscribeList.add(resourceDetail);
            } else if (OrderTypeEnum.NON_STANDARD.getCode().equals(resourceDetail.getSourceExtType())) {
                nonStandardList.add(resourceDetail);
            } else if (OrderTypeEnum.CHANGE.getCode().equals(resourceDetail.getSourceExtType())) {
                changeList.add(resourceDetail);
            } else if (OrderTypeEnum.EXTERNAL.getCode().equals(resourceDetail.getSourceExtType())) {
                externalList.add(resourceDetail);
            } else if (OrderTypeEnum.CORPORATE.getCode().equals(resourceDetail.getSourceExtType())) {
                corporateList.add(resourceDetail);
            } else if (OrderTypeEnum.DAG.getCode().equals(resourceDetail.getSourceExtType())) {
                dagList.add(resourceDetail);
            } else if (OrderTypeEnum.CORPORATE_CHANGE.getCode().equals(resourceDetail.getSourceExtType())) {
                dgChangeList.add(resourceDetail);
            } else if (OrderTypeEnum.EXTERNAL2.getCode().equals(resourceDetail.getSourceExtType())) {
                external2List.add(resourceDetail);
            } else if (OrderTypeEnum.EXTERNAL2_CHANGE.getCode().equals(resourceDetail.getSourceExtType())) {
                external2ChangeList.add(resourceDetail);
            }
        }
        if (CollectionUtil.isNotEmpty(subscribeList) || CollectionUtil.isNotEmpty(externalList)) {
            standardResourceDetailConsumer.consumeResourceMessage(subscribeList, externalList);
        }
        if (!CollectionUtil.isEmpty(changeList)) {
            changeResourceDetailConsumer.consumeResourceMessage(changeList);
        }
        if (CollectionUtil.isNotEmpty(dgChangeList)) {
            dgChangeResourceDetailConsumer.consumeResourceMessage(dgChangeList);
        }
        if (CollectionUtil.isNotEmpty(nonStandardList)) {
            nonStanderResourceDetailConsumer.consumeResourceMessage(nonStandardList);
        }
        if (CollectionUtil.isNotEmpty(corporateList)) {
            corporateResourceDetailConsumer.consumeResourceMessage(corporateList);
        }
        if (CollectionUtil.isNotEmpty(dagList)) {
            dagResourceDetailConsumer.consumeResourceMessage(dagList);
        }
        if (CollectionUtil.isNotEmpty(external2List)) {
            externalResourceDetailConsumer.consumeResourceMessage(external2List);
        }
        if (CollectionUtil.isNotEmpty(external2ChangeList)) {
            external2ChangeResourceDetailConsumer.consumeResourceMessage(external2ChangeList);
        }
    }
}
