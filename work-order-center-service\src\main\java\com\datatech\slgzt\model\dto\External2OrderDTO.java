package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 外部2工单DTO
 */
@Data
public class External2OrderDTO {

    private String id;

    private String orderCode;

    private BigDecimal orderAmount;

    //创建人
    private Long createBy;

    //创建人姓名
    private String createByName;

    //创建时间
    private LocalDateTime createTime;

    private Long jobExecutionId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;
}
