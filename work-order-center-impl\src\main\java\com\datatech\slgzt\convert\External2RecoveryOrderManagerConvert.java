package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.External2RecoveryOrderDO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface External2RecoveryOrderManagerConvert {


    External2RecoveryOrderDTO do2dto(External2RecoveryOrderDO external2RecoveryOrderDO);

    External2RecoveryOrderDO dto2do(External2RecoveryOrderDTO external2RecoveryOrderDTO);

}
