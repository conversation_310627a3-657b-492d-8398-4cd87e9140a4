package com.datatech.slgzt.model.vo.external2;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资源开通工单基础信息
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/13
 */

@Data
public class External2ChangeOrderVO {
    private String id;
    /**
     * 订单编号
     */
    @ExcelExportHeader(value = "订单编号")
    private String orderCode;

    /**
     * 创建者id
     */
    @JsonProperty("createdBy")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @ExcelExportHeader(value = "申请人名称")
    @JsonProperty("createdUserName")
    private String creator;

    private Long tenantId;

    private String tenantName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}

