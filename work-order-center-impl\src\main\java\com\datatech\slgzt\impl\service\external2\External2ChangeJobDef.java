package com.datatech.slgzt.impl.service.external2;

import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.External2ChangeOrderManager;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.query.External2ChangeOrderProductQuery;
import com.datatech.slgzt.service.external2.External2ChangeResourceService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 07月29日 10:10:22
 */
@Component
@Slf4j
public class External2ChangeJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;
    @Resource
    private External2ChangeJobListener listener;

    @Resource
    private External2ChangeOrderManager orderManager;

    @Resource
    private External2ChangeOrderProductManager productManager;

    @Autowired
    private List<External2ChangeResourceService> external2ChangeResourceServiceList;

    private final Map<String, External2ChangeResourceService> serviceMap = Maps.newHashMap();


    @Bean("external2ProductChangeJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("external2ProductChangeJob").incrementer(new RunIdIncrementer())
                .listener(listener)
                .start(external2ProductChangeInit())
                .next(external2EcsChange())
                .next(external2GcsChange())
                .next(external2EvsChange())
                .next(external2ShareEvsChange())
                .next(external2EipChange())
                .next(external2SlbChange())
                .next(external2RdsMysqlChange())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("external2ProductChangeInit")
    public Step external2ProductChangeInit() {
        return stepBuilderFactory.get("external2ProductChangeInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }


    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 根据orderId，获取external2RecoveryOrder
            External2ChangeOrderDTO orderDTO = orderManager.getById(orderId);
            // 通过orderId获取到product对象
            List<External2ChangeOrderProductDTO> productDTOs =
                    productManager.list(new External2ChangeOrderProductQuery()
                            .setWorkOrderId(orderId)
                            // 只发起主产品回收
                            .setParentProductId(0L)
                            .setProductType(productType.getCode()));
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<External2ChangeOrderProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                External2ChangeResourceService changeService = serviceMap.get(productType.getCode());
                External2ChangeOrderProductDTO productDTO = productDTOOptional.get();
                try {
                    // 开通
                    changeService.changeResource(orderDTO, Collections.singletonList(productDTO));
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    External2ChangeOrderProductDTO dto = new External2ChangeOrderProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setChangeStatus(ChangeTypeProductStatusEnum.CHANGE_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    productManager.update(dto);
                    // 发短信
//                    orderManager.sendFailSms(productDTO.getId());
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }

    @Bean("external2EcsChange")
    public Step external2EcsChange() {
        return stepBuilderFactory.get("external2EcsChange").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    @Bean("external2GcsChange")
    public Step external2GcsChange() {
        return stepBuilderFactory.get("external2GcsChange").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    @Bean("external2EvsChange")
    public Step external2EvsChange() {
        return stepBuilderFactory.get("external2EvsChange").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    @Bean("external2ShareEvsChange")
    public Step external2ShareEvsChange() {
        return stepBuilderFactory.get("external2ShareEvsChange").tasklet(getProductTasklet(ProductTypeEnum.SHARE_EVS)).build();
    }

    @Bean("external2EipChange")
    public Step external2EipChange() {
        return stepBuilderFactory.get("external2EipChange").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    @Bean("external2SlbChange")
    public Step external2SlbChange() {
        return stepBuilderFactory.get("external2SlbChange").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }

    @Bean("external2RdsMysqlChange")
    public Step external2RdsMysqlChange() {
        return stepBuilderFactory.get("external2RdsMysqlChange").tasklet(getProductTasklet(ProductTypeEnum.RDS_MYSQL)).build();
    }

    private Optional<External2ChangeOrderProductDTO> filterProduct(List<External2ChangeOrderProductDTO> productDTOs) {
        return productDTOs.stream().filter(i ->
                i.getParentProductId() == 0
                        && (// 没有回收状态 或者 待更变/变更失败
                        i.getChangeStatus() == null ||
                                (ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode().equals(i.getChangeStatus())
                                        || ChangeTypeProductStatusEnum.CHANGE_FAIL.getCode().equals(i.getChangeStatus())))
        ).findFirst();
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        for (External2ChangeResourceService external2ChangeResourceService : external2ChangeResourceServiceList) {
            serviceMap.put(external2ChangeResourceService.registerOpenService().getCode(), external2ChangeResourceService);
        }
    }
}
