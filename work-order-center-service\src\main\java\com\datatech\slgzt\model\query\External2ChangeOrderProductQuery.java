package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 外部2变更工单产品查询对象
 */
@Data
@Accessors(chain = true)
public class External2ChangeOrderProductQuery {

    /** 工单ID */
    private String workOrderId;

    /** 创建的工单ID */
    private String createWorkOrderId;

    /** 更变类型 */
    private String changeType;

    /** 产品类型 */
    private String productType;

    /** 父类产品ID */
    private Long parentProductId;

    /** GID */
    private String gid;

    /** 子订单ID */
    private Long subOrderId;

    /** 开通状态 */
    private String changeStatus;

    /** 资源详情ID */
    private String resourceDetailId;

    /** 屏蔽告警 */
    private Boolean blockWarning;

    /** 租户确认 */
    private Boolean tenantConfirm;

    /** 串行变更状态 */
    private Integer serialChangeStatus;

    /** ID列表 */
    private List<Long> idList;

    /** 工单ID列表 */
    private List<String> workOrderIdList;

    /** 创建时间开始 */
    private LocalDateTime createTimeStart;

    /** 创建时间结束 */
    private LocalDateTime createTimeEnd;

    /** 修改时间开始 */
    private LocalDateTime modifyTimeStart;

    /** 修改时间结束 */
    private LocalDateTime modifyTimeEnd;

    /** 页码 */
    private Integer pageNum = 1;

    /** 每页大小 */
    private Integer pageSize = 10;

    private Collection<Long> subOrderIds;
}
