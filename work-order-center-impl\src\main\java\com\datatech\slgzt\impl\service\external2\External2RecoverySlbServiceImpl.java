package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.layout.ResRecoveryReqModel;
import com.datatech.slgzt.model.recovery.RecoveryEipModel;
import com.datatech.slgzt.model.recovery.RecoverySlbModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2RecoveryResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.UuidUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @program: workordercenterproject
 * @description: 负载均衡组合回收
 * @author: Lzj
 * @create: 2025-03-25 09:59
 **/
@Service
@Slf4j
public class External2RecoverySlbServiceImpl implements External2RecoveryResourceService {

    @Resource
    private PlatformService platformService;

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private BusinessService businessService;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void recoveryResource(External2RecoveryOrderDTO dto, List<External2RecoveryOrderProductDTO> productDTOs) {
        for (External2RecoveryOrderProductDTO product : productDTOs) {
            ResRecoveryReqModel resRecoveryReqModel = new ResRecoveryReqModel();
            //参数封装
            List<ResRecoveryReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            Boolean syncRecovery = product.getSyncRecovery();
            if (ProductTypeEnum.SLB.getCode().equals(product.getProductType())) {
                RecoverySlbModel recoverySlbModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoverySlbModel.class);
                //基础参数封装
                baseParamInit(recoverySlbModel, resRecoveryReqModel, product, dto);
                //ecs参数填充
                ResRecoveryReqModel.ProductOrder slbProductOrder = new ResRecoveryReqModel.ProductOrder();
                slbProductOrder.setProductOrderId(recoverySlbModel.getProductOrderId().toString());
                slbProductOrder.setProductOrderType("SLB_DELETE");
                slbProductOrder.setProductType(ProductTypeEnum.SLB.getCode());
                slbProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                slbProductOrder.setGId(recoverySlbModel.getSlbId());
                ResRecoveryReqModel.Attrs slbAttrs = new ResRecoveryReqModel.Attrs();
                slbAttrs.setResourceId(recoverySlbModel.getSlbId());
                slbAttrs.setGId(recoverySlbModel.getSlbId());
                slbAttrs.setOptUuid(UuidUtil.getUUID());
                slbProductOrder.setAttrs(slbAttrs);
                reqProductList.add(slbProductOrder);
                //组合回收则封装所有参数，否则仅回收主产品，同时解绑eip
                if (Objects.isNull(syncRecovery) || syncRecovery) {
                    //eip参数填充
                    RecoveryEipModel eipModel = recoverySlbModel.getEipModel();
                    if (Objects.nonNull(eipModel)) {
                        ResRecoveryReqModel.ProductOrder eipProductOrder = new ResRecoveryReqModel.ProductOrder();
                        eipProductOrder.setProductOrderId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderType("EIP_DELETE");
                        eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
                        eipProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                        eipProductOrder.setGId(eipModel.getEipId());
                        ResRecoveryReqModel.Attrs eipAttrs = new ResRecoveryReqModel.Attrs();
                        eipAttrs.setType(ProductTypeEnum.SLB.getCode().toUpperCase());
                        eipAttrs.setResourceId(eipModel.getEipId());
                        eipAttrs.setGId(eipModel.getEipId());
                        eipAttrs.setEcsResourceId(recoverySlbModel.getSlbId());
                        eipProductOrder.setAttrs(eipAttrs);
                        reqProductList.add(eipProductOrder);
                    }
                }
            }
            //调用任务中心回收资源
            resRecoveryReqModel.setProductOrders(reqProductList);
            log.info("对公资源回收，callLayoutOrder--调用编排中心初始化start 入参：{}", JSONObject.toJSONString(resRecoveryReqModel));
            log.info("对公资源回收，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resRecoveryReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "对公资源回收失败，callLayoutOrder--编排中心初始化返回结果失败");
            log.info("对公资源回收，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
            //根据是否同步回收更新产品回收状态
            productManager.updateStatusByIds(ListUtil.toList(product.getId()), RecoveryStatusEnum.RECOVERING.getType());
            if (Objects.isNull(syncRecovery) || syncRecovery) {
                productManager.updateStatusByParentId(product.getId(), RecoveryStatusEnum.RECOVERING.getType());
            } else {
                productManager.updateStatusByParentId(product.getId(), RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            }
        }
//        //把对应的产品都改成开通中状态
//        List<Long> ids = recoveryWorkOrderProducts.stream().map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
//        manager.updateStatusByIds(ids, String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));

    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.SLB;
    }


    private void baseParamInit(RecoverySlbModel recoverySlbModel,
                               ResRecoveryReqModel resRecoveryReqModel,
                               External2RecoveryOrderProductDTO product,
                               External2RecoveryOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(recoverySlbModel.getBillId(), recoverySlbModel.getRegionCode());
        //设置计费号
        resRecoveryReqModel.setAccount(recoverySlbModel.getBillId());
        //设置业务code;
        resRecoveryReqModel.setSourceExtType(OrderTypeEnum.RECOVERY.getCode());
        //设置业务code
        resRecoveryReqModel.setBusinessCode("DEFAULT");
        // 根据业务系统id获取业务系统
        CmpAppDTO cmpAppDTO = businessService.getById(recoverySlbModel.getBusinessSystemId());
        resRecoveryReqModel.setBusinessSystemCode(cmpAppDTO.getSystemCode());
        //设置客户id
        resRecoveryReqModel.setCustomId(recoverySlbModel.getCustomNo());
        //设置区域编码
        resRecoveryReqModel.setRegionCode(recoverySlbModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resRecoveryReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resRecoveryReqModel.setTenantId(tenantId);
        //设置userId
        resRecoveryReqModel.setUserId(dto.getCreatorId());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resRecoveryReqModel.setTaskSource(8);
    }
}
