package com.datatech.slgzt.impl.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.External2OrderProductManager;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.external2.External2OrderService;
import com.datatech.slgzt.service.external2.External2ResOpenService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Slf4j
@Configuration
public class External2CreateJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<External2ResOpenService> resOpenServiceList;

    @Resource
    private External2CreateJobListener jobListener;

    private final Map<String, External2ResOpenService> serviceMap = Maps.newHashMap();

    @Resource
    private External2OrderProductManager productManager;

    @Resource
    private External2OrderService orderService;

    @Resource
    private CmdbReportService cmdbReportService;


    @Bean("external2ProductCreateJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("external2ProductCreateJob").incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(external2ProductCreateInit())
                .next(external2CloudPortCreate())
                .next(external2EcsCreate())
                .next(external2RedisCreate())
                .next(external2PostgreSqlCreate())
                .next(external2MysqlCreate())
                .next(external2GcsCreate())
                .next(external2RdsMysqlCreate())
                .next(external2SlbCreate())
                .next(external2NatCreate())
                .next(external2EipCreate())
                .next(external2EvsCreate())
                .next(external2ShareEvsCreate())
                .next(external2ObsCreate())
                .next(external2VpnCreate())
                .next(external2BackupCreate())
                .next(external2Cmdb())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("external2ProductCreateInit")
    public Step external2ProductCreateInit() {
        return stepBuilderFactory.get("external2ProductCreateInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean("external2EcsCreate")
    public Step external2EcsCreate() {
        return stepBuilderFactory.get("external2EcsCreate").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    @Bean("external2RedisCreate")
    public Step external2RedisCreate() {
        return stepBuilderFactory.get("external2RedisCreate").tasklet(getProductTasklet(ProductTypeEnum.REDIS)).build();
    }

    @Bean("external2PostgreSqlCreate")
    public Step external2PostgreSqlCreate() {
        return stepBuilderFactory.get("external2PostgreSqlCreate").tasklet(getProductTasklet(ProductTypeEnum.POSTGRESQL)).build();
    }

    @Bean("external2MysqlCreate")
    public Step external2MysqlCreate() {
        return stepBuilderFactory.get("external2MysqlCreate").tasklet(getProductTasklet(ProductTypeEnum.MYSQL)).build();
    }

    @Bean("external2GcsCreate")
    public Step external2GcsCreate() {
        return stepBuilderFactory.get("external2GcsCreate").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    @Bean("external2RdsMysqlCreate")
    public Step external2RdsMysqlCreate() {
        return stepBuilderFactory.get("external2RdsMysqlCreate").tasklet(getProductTasklet(ProductTypeEnum.RDS_MYSQL)).build();
    }

    @Bean("external2SlbCreate")
    public Step external2SlbCreate() {
        return stepBuilderFactory.get("external2SlbCreate").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }


    @Bean("external2NatCreate")
    public Step external2NatCreate() {
        return stepBuilderFactory.get("external2NatCreate").tasklet(getProductTasklet(ProductTypeEnum.NAT)).build();
    }

    @Bean("external2EipCreate")
    public Step external2EipCreate() {
        return stepBuilderFactory.get("external2EipCreate").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    @Bean("external2EvsCreate")
    public Step external2EvsCreate() {
        return stepBuilderFactory.get("external2EvsCreate").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    @Bean("external2ShareEvsCreate")
    public Step external2ShareEvsCreate() {
        return stepBuilderFactory.get("external2ShareEvsCreate").tasklet(getProductTasklet(ProductTypeEnum.SHARE_EVS)).build();
    }

    @Bean("external2ObsCreate")
    public Step external2ObsCreate() {
        return stepBuilderFactory.get("external2ObsCreate").tasklet(getProductTasklet(ProductTypeEnum.OBS)).build();
    }

    @Bean("external2VpnCreate")
    public Step external2VpnCreate() {
        return stepBuilderFactory.get("external2VpnCreate").tasklet(getProductTasklet(ProductTypeEnum.VPN)).build();
    }

    @Bean("external2BackupCreate")
    public Step external2BackupCreate() {
        return stepBuilderFactory.get("external2BackupCreate").tasklet(getProductTasklet(ProductTypeEnum.BACKUP)).build();
    }

    @Bean("external2Cmdb")
    public Step external2Cmdb() {
        return stepBuilderFactory.get("external2Cmdb").tasklet((contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // consumer回调会insert resource detail，
            // 它必然在layoutModify之前，layoutModify完事就重启
            Thread.sleep(40000);
            cmdbReportService.createInstanceOfCorporate(orderId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 通过orderId获取到product对象
            List<External2OrderProductDTO> productDTOs = productManager.list(new External2OrderProductQuery()
                    .setOrderId(orderId)
                    .setProductType(productType.getCode()));
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<External2OrderProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                External2ResOpenService createService = serviceMap.get(productType.getCode());
                External2OrderProductDTO productDTO = productDTOOptional.get();
                try {
                    // 开通
                    createService.openResource(productDTO);
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    External2OrderProductDTO dto = new External2OrderProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    productManager.update(dto);
                    // 发短信
                    orderService.sendFailSms(productDTO.getId());
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }

    // cloudPort创建
    @Bean("external2CloudPortCreate")
    public Step external2CloudPortCreate() {
        DefaultTransactionAttribute attribute = new DefaultTransactionAttribute();
        attribute.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
        return stepBuilderFactory.get("external2CloudPortCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            //通过orderId 获取到VPC创建的对象
            List<External2OrderProductDTO> cloudPortDTOList = productManager.list(new External2OrderProductQuery()
                    .setOrderId(orderId)
                    .setProductType("cloudPort"));
            //如果ecsDTOList不为空，说明已经创建完ecs了，直接返回
            if (ObjNullUtils.isNull(cloudPortDTOList)) {
                return RepeatStatus.FINISHED;
            }
            cloudPortDTOList.forEach(external2OrderProductDTO -> {
                try {
                    External2ResOpenService createService = serviceMap.get("cloudPort");
                    createService.openResource(external2OrderProductDTO);
                    //更新产品表
                    external2OrderProductDTO.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
                    productManager.update(external2OrderProductDTO);
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", external2OrderProductDTO, ExceptionUtils.getStackTrace(e));
                    //更新产品表
                    external2OrderProductDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    external2OrderProductDTO.setMessage(e.getMessage());
                    productManager.update(external2OrderProductDTO);
                    //已经这个节点关闭事务直接抛出异常即可
                    throw e;
                }

            });
            return RepeatStatus.FINISHED;
        }).transactionAttribute(attribute).build();
    }


    @Override
    public void afterPropertiesSet() {
        for (External2ResOpenService service : resOpenServiceList) {
            serviceMap.put(service.registerOpenService().getCode(), service);
        }
    }

    private Optional<External2OrderProductDTO> filterProduct(List<External2OrderProductDTO> productDTOs) {
        return productDTOs.stream().filter(i -> i.getParentProductId() == 0
                && (ResOpenEnum.WAIT_OPEN.getCode().equals(i.getOpenStatus()) || ResOpenEnum.OPEN_FAIL.getCode().equals(i.getOpenStatus()))
        ).findFirst();
    }
}
