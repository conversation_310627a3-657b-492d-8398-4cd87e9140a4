package com.datatech.slgzt.model.req.external2;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class External2OrderPageReq {

    private String id;

    private String orderCode;

    private String tenantName;

    private LocalDateTime createTimeEnd;

    private LocalDateTime createTimeStart;

    private Long createBy;

    private String createByName;

    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    @NotNull(message = "每页大小不能为空")
    private Integer pageSize = 10;
} 