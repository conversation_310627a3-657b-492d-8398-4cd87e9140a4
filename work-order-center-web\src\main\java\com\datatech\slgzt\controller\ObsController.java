package com.datatech.slgzt.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.vo.obs.BucketVO;
import com.datatech.slgzt.utils.Precondition;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @program: workordercenterproject
 * @description: obs
 * @author: LK
 * @create: 2025-09-18 17:32
 **/
@RestController
@RequestMapping("/obs")
@Slf4j
public class ObsController {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @GetMapping("/bucketMonitor")
    public CommonResult<BucketVO> bucketMonitor(@RequestParam Long id, Integer time) {
        BucketVO bucketVO = new BucketVO();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.selectResourceDetailOfObs(id);
        String endpoint = resourceDetailDTO.getEndpoint();
        String clusterName = resourceDetailDTO.getClusterName();
        String deviceName = resourceDetailDTO.getDeviceName();
        bucketVO.setBucketName(deviceName);
        //登录获取token
        Map<String, String> tokenMap = getToken(endpoint, resourceDetailDTO.getUsername(), resourceDetailDTO.getPassword());
        Precondition.checkArgument(CollectionUtil.isNotEmpty(tokenMap), "获取token异常");
        String Cookie = tokenMap.get("Cookie");
        String X_XSRF_TOKEN = tokenMap.get("X-XSRF-TOKEN");
        //查询桶使用信息
        Map<String, String> bucket = findBucket(endpoint + "/api/v3/onestor/" + clusterName + "/objectstorage/bucket", deviceName, Cookie, X_XSRF_TOKEN);
        if (Objects.nonNull(bucket)) {
            bucketVO.setQuota(bucket.get("size_actual"));
            bucketVO.setUseQuota(bucket.get("size_utilized"));
            bucketVO.setUseQuotaPercent(bucket.get("usage_rate"));
        }
        //查询ops、带宽指标
        bucketVO.setOpsUpload(queryGraphiteMetrics(endpoint + "/graphite/render", deviceName, "ops.upload", time));
        bucketVO.setOpsDownload(queryGraphiteMetrics(endpoint + "/graphite/render", deviceName, "ops.download", time));
        bucketVO.setBandwidthUpload(queryGraphiteMetrics(endpoint + "/graphite/render", deviceName, "bw.upload", time));
        bucketVO.setBandwidthUpload(queryGraphiteMetrics(endpoint + "/graphite/render", deviceName, "bw.download", time));
        return CommonResult.success(bucketVO);
    }

    private Map<String, String> getToken(String baseUrl, String username, String password) {
        Map<String, String> map = new HashMap<>();
        try {
            String xsrfToken, calamariSessionId;
            // 登录并获取所有 Cookies
            Map<String, String> cookies = loginAndGetCookies(baseUrl + "/api/v1/auth/login", username, password);

            // 获取特定的 Cookie 值
            xsrfToken = getCookieValue(cookies, "XSRF-TOKEN");
            calamariSessionId = getCookieValue(cookies, "calamari_sessionid");
//0YfrNYsWWpoUE7oLgpIqIQ47ZR9rAVTLLuJjG6tbZwcfojPuvh1d1fbtxVutaWM5
            //vpf1jhd72kdw59ildsro3k71q0bsj3ld
            log.info("XSRF-TOKEN: {}, calamari_sessionid：{}", xsrfToken, calamariSessionId);

            // 如果没有直接找到，可能是因为 Cookie 名称有大小写问题，尝试查找
            if (xsrfToken == null) {
                xsrfToken = findCookieIgnoreCase(cookies, "XSRF-TOKEN");
            }
            if (calamariSessionId == null) {
                calamariSessionId = findCookieIgnoreCase(cookies, "calamari_sessionid");
            }

            log.info("最终获取的 XSRF-TOKEN: {}，calamari_sessionid: {}", xsrfToken, calamariSessionId);
            map.put("Cookie", String.format("calamari_sessionid=%s; XSRF-TOKEN=%s", calamariSessionId, xsrfToken));
            map.put("X-XSRF-TOKEN", xsrfToken);
        } catch (Exception e) {
            log.error("登陆获取token失败：{}", e.getMessage());
        }
        return map;
    }

    public Map<String, String> findBucket(String baseUrl, String targetBucketName,
                                          String cookie, String xsrfToken) {
        RestTemplate restTemplate = new RestTemplate();
        ObjectMapper objectMapper = new ObjectMapper();
        int pageNum = 1;

        try {
            while (true) {
                // 安全构建 URL，自动处理编码
                UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
                        .queryParam("page_num", pageNum)
                        .queryParam("page_size", 1000)
                        .queryParam("search_key", "name")
                        .queryParam("search_value", targetBucketName);

                String url = builder.build().toUriString();

                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.set("Cookie", cookie);
                headers.set("X-XSRF-TOKEN", xsrfToken);
                headers.set("Content-Type", "application/json");

                HttpEntity<String> requestEntity = new HttpEntity<>(headers);

                log.info("请求 URL: {}", url);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);

                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    JsonNode rootNode = objectMapper.readTree(response.getBody());
                    JsonNode dataArray = rootNode.path("data").path("data");

                    if (!dataArray.isArray()) {
                        log.warn("响应中 data.data 不是数组，格式异常");
                        break;
                    }

                    // 遍历当前页的桶数据
                    for (JsonNode bucketNode : dataArray) {
                        String name = bucketNode.path("name").asText(null);
                        if (targetBucketName.equals(name)) {
                            String sizeActual = bucketNode.path("size_actual").asText("");
                            String sizeUtilizedStr = bucketNode.path("size_utilized").asText("");
                            String maxSizeStr = bucketNode.path("max_size").asText("");

                            // 解析为字节数
                            long sizeUtilizedBytes = parseSizeToBytes(sizeUtilizedStr);
                            long maxSizeBytes = parseSizeToBytes(maxSizeStr);

                            // 计算使用率
                            String usageRate = "0.00%";
                            if (maxSizeBytes > 0) {
                                double rate = (double) sizeUtilizedBytes / maxSizeBytes;
                                usageRate = String.format("%.2f%%", rate * 100);
                            }

                            // 构建结果 Map
                            Map<String, String> result = new HashMap<>();
                            result.put("name", name);
                            result.put("size_actual", sizeActual);
                            result.put("size_utilized", sizeUtilizedStr);
                            result.put("max_size", maxSizeStr);
                            result.put("usage_rate", usageRate);

                            log.info("成功找到 Bucket 并计算使用率: {}", result);
                            return result;
                        }
                    }

                    // 检查是否有下一页
                    JsonNode nextPageNode = rootNode.path("data").path("next_page");
                    if (nextPageNode.isNull()) {
                        break; // 没有下一页
                    }
                    // 下一页页码
                    pageNum = nextPageNode.asInt();
                } else {
                    log.error("HTTP 请求失败，状态码: {}", response.getStatusCode());
                    break;
                }
            }
        } catch (Exception e) {
            log.error("查询 Bucket 异常: {}", e.getMessage(), e);
        }

        log.warn("未找到匹配的 Bucket: {}", targetBucketName);
        // 未找到
        return null;
    }

    /**
     * 查询 Graphite 中指定桶的指标数据
     *
     * @param baseUrl     Graphite 接口基础地址，如 http://188.106.140.81/graphite/render
     * @param bucketName  桶名称，动态传入
     * @param metricType  指标类型，如 ops.upload, ops.download, bw.upload, bw.download
     * @param minutes     查询时间范围，如 30 表示 from=-30minute
     * @return Map<String, Object> 包含 target 和 data（时间 -> 值）
     */
    public List<BucketVO.DataPoint> queryGraphiteMetrics(String baseUrl, String bucketName, String metricType, int minutes) {
        RestTemplate restTemplate = new RestTemplate();
        ObjectMapper objectMapper = new ObjectMapper();
        List<BucketVO.DataPoint> list = new ArrayList<>();
        try {
            // 构建 URL
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .queryParam("format", "json")
                    .queryParam("from", "-" + minutes + "minute")
                    .queryParam("target", "rgw_bucket.default.admin." + bucketName + "." + metricType)
                    .queryParam("rgw_bucket_name", bucketName);

            String url = builder.build().toUriString();
            log.info("请求 Graphite URL: {}", url);

            HttpEntity<String> requestEntity = new HttpEntity<>(new org.springframework.http.HttpHeaders());

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());

                if (!rootNode.isArray() || rootNode.isEmpty()) {
                    log.warn("Graphite 返回数据为空或格式异常");
                    return null;
                }

                JsonNode firstSeries = rootNode.get(0);
                JsonNode datapoints = firstSeries.path("datapoints");

                if (!datapoints.isArray()) {
                    log.warn("datapoints 不是数组，格式异常");
                    return null;
                }

                // 时间格式化器
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 可根据需要调整时区
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));

                // 存储时间字符串 -> 值 的映射
                // 保持时间顺序

                for (JsonNode point : datapoints) {
                    BucketVO.DataPoint data = new BucketVO.DataPoint();
                    if (point.isArray() && point.size() >= 2) {
                        // null 补 0
                        double value = point.get(0).isNull() ? 0.0 : point.get(0).asDouble();
                        long timestamp = point.get(1).asLong();
                        // 秒转毫秒
                        String timeStr = sdf.format(new Date(timestamp * 1000L));
                        data.setPoint(timeStr);
                        data.setData(value);
                        list.add(data);
                    }
                }

                log.info("成功获取指标数据，共 {} 个时间点", list.size());
                return list;

            } else {
                log.error("Graphite 请求失败，状态码: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("查询 Graphite 数据异常: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 登录并获取 Cookies
     */
    public Map<String, String> loginAndGetCookies(String url, String username, String password) {
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体
        String requestBody = String.format("{\"username\": \"%s\", \"password\": \"%s\"}", username, password);

        // 创建请求实体
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            // 发送 POST 请求
            log.info("请求url：{}，参数：{}", url, JSON.toJSONString(requestBody));
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 获取响应头中的 Set-Cookie
            List<String> setCookieHeaders = response.getHeaders().get("Set-Cookie");

            // 解析 Cookies
            return parseCookies(setCookieHeaders);

        } catch (Exception e) {
            throw new RuntimeException("登录请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析 Set-Cookie 头信息
     */
    private Map<String, String> parseCookies(List<String> setCookieHeaders) {
        Map<String, String> cookies = new java.util.HashMap<>();

        if (setCookieHeaders != null) {
            for (String cookieHeader : setCookieHeaders) {
                String[] cookieParts = cookieHeader.split(";");
                if (cookieParts.length > 0) {
                    String[] nameValue = cookieParts[0].split("=", 2);
                    if (nameValue.length == 2) {
                        String cookieName = nameValue[0].trim();
                        String cookieValue = nameValue[1].trim();
                        cookies.put(cookieName, cookieValue);
                    }
                }
            }
        }

        return cookies;
    }

    /**
     * 获取特定的 Cookie 值
     */
    public String getCookieValue(Map<String, String> cookies, String cookieName) {
        return cookies.get(cookieName);
    }

    /**
     * 忽略大小写查找 Cookie
     */
    private String findCookieIgnoreCase(Map<String, String> cookies, String targetName) {
        for (Map.Entry<String, String> entry : cookies.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(targetName)) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 将带单位的大小字符串转换为字节数
     * 支持：B, KB, MB, GB, TB（不区分大小写）
     * 示例： "1GB" -> 1_073_741_824
     */
    private long parseSizeToBytes(String sizeStr) {
        if (sizeStr == null || sizeStr.isEmpty()) {
            return 0;
        }

        // 去除空格并转大写
        sizeStr = sizeStr.trim().toUpperCase();

        // 提取数字部分（支持小数）
        String numberPart = sizeStr.replaceAll("[^\\d.]", "");
        double value;
        try {
            value = Double.parseDouble(numberPart);
        } catch (NumberFormatException e) {
            return 0;
        }

        // 判断单位
        if (sizeStr.contains("TB")) {
            return (long) (value * 1024 * 1024 * 1024 * 1024);
        } else if (sizeStr.contains("GB")) {
            return (long) (value * 1024 * 1024 * 1024);
        } else if (sizeStr.contains("MB")) {
            return (long) (value * 1024 * 1024);
        } else if (sizeStr.contains("KB")) {
            return (long) (value * 1024);
        } else {
            // 默认是 B
            return (long) value;
        }
    }


}
