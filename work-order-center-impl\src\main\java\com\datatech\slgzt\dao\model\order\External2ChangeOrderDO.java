package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2变更工单表
 */
@Data
@TableName("WOC_EXTERNAL2_CHANGE_ORDER")
public class External2ChangeOrderDO {

    @TableField("ID")
    private String id;

    // 订单编号
    @TableField("ORDER_CODE")
    private String orderCode;

    //租户id
    @TableField("TENANT_ID")
    private Long tenantId;

    //租户名称
    @TableField("TENANT_NAME")
    private String tenantName;

    //创建人
    @TableField("CREATOR")
    private String creator;

    //创建时间
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    //创建人id
    @TableField("CREATOR_ID")
    private Long creatorId;

    // jobExecutionId
    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;

    @TableField("BILL_ID")
    private String billId;
}
