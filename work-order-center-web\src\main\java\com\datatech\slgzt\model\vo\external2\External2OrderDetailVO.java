package com.datatech.slgzt.model.vo.external2;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月12日 20:25:02
 */
@Data
public class External2OrderDetailVO {

    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<MysqlV2Model> rdsMysqlModelList;

    private List<EcsModel> redisModelList;

    /**
     * mysql申请资源列表的json,gcs
     */
    private List<EcsModel> mysqlModelList;

    /**
     * postgreSql申请资源列表的json,gcs
     */
    private List<EcsModel> postgreSqlModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> shareEvsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 容器资源配额申请json
     */
    private List<CQModel> cqModelList;

    /**
     * 备份策略申请json
     */
    private List<BackupModel> backupModelList;

    /**
     * 云端口
     */
    private List<CloudPortModel> cloudPortModelList;

    /**
     * vpn
     */
    private List<VpnModel> vpnModelList;
}
