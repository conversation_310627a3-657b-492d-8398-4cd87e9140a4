package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.External2ChangeOrderDO;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import org.mapstruct.Mapper;

/**
 * 外部2变更工单Manager转换器
 */
@Mapper(componentModel = "spring")
public interface External2ChangeOrderManagerConvert {

    /**
     * DTO 转 DO
     */
    External2ChangeOrderDO dto2do(External2ChangeOrderDTO dto);

    /**
     * DO 转 DTO
     */
    External2ChangeOrderDTO do2dto(External2ChangeOrderDO external2ChangeOrderDO);
}
