package com.datatech.slgzt.controller.external2;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.External2ChangeOrderWebConvert;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.External2ChangeOrderManager;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.opm.External2ChangeOrderCreateOpm;
import com.datatech.slgzt.model.query.External2ChangeOrderProductQuery;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.change.ChangeWorkOrderDetailReq;
import com.datatech.slgzt.model.req.change.ChangeWorkOrderPageReq;
import com.datatech.slgzt.model.req.external2.change.External2ChangeOrderCreateReq;
import com.datatech.slgzt.model.req.external2.change.External2ChangeOrderPageReq;
import com.datatech.slgzt.model.req.recovery.RecoveryWorkOrderDetailReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.external2.External2ChangeOrderVO;
import com.datatech.slgzt.service.external2.External2ChangeOrderService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 更变工单控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月30日 13:47:40
 */
@Slf4j
@RestController
@RequestMapping("/external2/changeWorkOrder")
public class External2ChangeOrderController {

    @Resource
    private External2ChangeOrderWebConvert convert;

    @Resource
    private External2ChangeOrderService orderService;
    @Resource
    private External2ChangeOrderManager orderManager;
    @Resource
    private External2ChangeOrderProductManager productManager;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    /**
     * 创建更变工单
     */
    @RequestMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(description = "创建对公更变工单", operationType = "CREATE")
    public CommonResult<String> create(@RequestBody External2ChangeOrderCreateReq req) throws ExecutionException, InterruptedException {
        //校验基础入参
        External2ChangeOrderCreateOpm opm = convert.convert(req);
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        opm.setCreator(currentUser.getUserName());
        opm.setCreatorId(currentUser.getId());
        // 对变更的工单进行校验，是否允许变更创建
        orderService.checkChangeOrderCanCreate(opm);
        String orderId = orderService.createChangeWorkOrder(opm);
        return CommonResult.success(orderId);
    }

    /**
     * 变更列表查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<External2ChangeOrderVO>> page(@RequestBody External2ChangeOrderPageReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageNum()), "页码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageSize()), "每页大小不能为空");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "当前用户未登录");
        List<Long> tenantIdList = tenantManager.listRelTenantIdByUserId(currentUser.getId());
        External2ChangeOrderQuery query = convert.pageReq2Query(req);
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))
                && !roles.contains("general_admin")) {
            query.setTenantIds(tenantIdList);
        }
        PageResult<External2ChangeOrderDTO> page = orderService.page(query, currentUser.getId());
        PageResult<External2ChangeOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        return CommonResult.success(box);
    }

    /**
     * 详情
     */
    @RequestMapping("/detail")
    public CommonResult<ChangeWorkOrderDetailVO> detail(@RequestBody ChangeWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        External2ChangeOrderDTO dto = orderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        ChangeWorkOrderDetailVO vo = convert.convertDetail(dto);
        List<External2ChangeOrderProductDTO> productDTOS = productManager.list(new External2ChangeOrderProductQuery()
                .setWorkOrderId(req.getWorkOrderId()));
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream().filter(productDTO -> productDTO.getParentProductId() == 0).collect(Collectors.toList());
        ArrayListMultimap<String, External2ChangeOrderProductDTO> type2product =
                StreamUtils.toArrayListMultimap(productDTOS, External2ChangeOrderProductDTO::getProductType);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(External2ChangeOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        convert.fillProductDetail(vo, resourceDetailDTOS, type2product, dto.getCreatorId(), currentUser);

        return CommonResult.success(vo);
    }


    /**
     * 导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody ChangeWorkOrderPageReq req, HttpServletResponse response) {
//        Precondition.checkArgument(req.getApprovalCode(), "查询审批节点的类型不能为空");
//        ChangeWorkOrderQuery orderQuery = convert.pageReq2Query(req);
//        orderQuery.setPageSize(10000);
//        Long userId = UserHelper.INSTANCE.getCurrentUserId();
//        Precondition.checkArgument(userId, "当前用户未登录");
//        PageResult<ChangeWorkOrderDTO> page = changeWorkOrderService.page(orderQuery, userId);
//        PageResult<ChangeWorkOrderVO> box = PageWarppers.box(page, convert::dto2vo);
//        List<ChangeWorkOrderVO> records = box.getRecords();
//        String filePath = System.getProperty("user.dir") + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
//        FileUtils.doExport(records, ChangeWorkOrderVO.class, filePath);
//        downloadFile(response, filePath, "变更工单列表.xlsx");
    }

    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("文件下载失败：" + e);
        }
    }


    @RequestMapping(value = "/restart", method = RequestMethod.POST)
    public CommonResult<String> restart(@RequestBody RecoveryWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        orderService.restart(req.getWorkOrderId());
        return CommonResult.success(req.getWorkOrderId());
    }
}
