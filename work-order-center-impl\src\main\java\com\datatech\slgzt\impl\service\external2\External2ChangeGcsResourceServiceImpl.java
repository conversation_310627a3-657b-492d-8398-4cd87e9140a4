package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.model.change.ChangeEcsModel;
import com.datatech.slgzt.model.change.ChangeEipModel;
import com.datatech.slgzt.model.change.ChangeEvsModel;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.layout.ResChangeReqModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2ChangeResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: gpu云主机规格变更
 * @author: LK
 * @create: 2025-04-08 17:15
 **/
@Service
@Slf4j
public class External2ChangeGcsResourceServiceImpl implements External2ChangeResourceService {

    @Resource
    private PlatformService platformService;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    @Resource
    private External2ChangeOrderProductManager changeWorkOrderProductManager;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void changeResource(External2ChangeOrderDTO dto, List<External2ChangeOrderProductDTO> changeWorkOrderProducts) {
        for (External2ChangeOrderProductDTO product : changeWorkOrderProducts) {
            ResChangeReqModel resChangeReqModel = new ResChangeReqModel();
            //参数封装
            List<ResChangeReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            if (ProductTypeEnum.GCS.getCode().equals(product.getProductType())) {
                ChangeEcsModel changeGcsModel = JSONObject.parseObject(product.getPropertySnapshot(), ChangeEcsModel.class);
                //基础参数封装
                baseParamInit(changeGcsModel, resChangeReqModel, product, dto);
                //规格变更封装云主机
                if (product.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    ResChangeReqModel.ProductOrder gcsProductOrder = new ResChangeReqModel.ProductOrder();
                    gcsProductOrder.setGId(changeGcsModel.getProductOrderId().toString());
                    gcsProductOrder.setProductOrderId(changeGcsModel.getProductOrderId().toString());
                    gcsProductOrder.setProductOrderType("ECS_MODIFY");
                    gcsProductOrder.setProductType(ProductTypeEnum.ECS.getCode());
                    gcsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                    ResChangeReqModel.Attrs gcsAttrs = new ResChangeReqModel.Attrs();
                    gcsAttrs.setResourceId(changeGcsModel.getVmId());
                    gcsAttrs.setFlavorId(changeGcsModel.getChangeFlavorId());
                    gcsAttrs.setFlavorCode(changeGcsModel.getChangeFlavorId());
                    gcsProductOrder.setAttrs(gcsAttrs);
                    reqProductList.add(gcsProductOrder);
                }
                //存储变更封装云硬盘
                if (product.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    List<ChangeEvsModel> evsModelList = changeGcsModel.getEvsModelList();
                    if (CollectionUtil.isNotEmpty(evsModelList)) {
                        evsModelList.forEach(evs -> {
                            ResChangeReqModel.ProductOrder evsProductOrder = new ResChangeReqModel.ProductOrder();
                            evsProductOrder.setGId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderType("EVS_MODIFY");
                            evsProductOrder.setProductType(ProductTypeEnum.EVS.getCode());
                            evsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                            ResChangeReqModel.Attrs evsAttrs = new ResChangeReqModel.Attrs();
                            evsAttrs.setResourceId(evs.getEvsId());
                            evsAttrs.setVolumeSize(evs.getChangeVolumeSize());
                            evsProductOrder.setAttrs(evsAttrs);
                            reqProductList.add(evsProductOrder);
                        });
                    }
                }
                //带宽变更封装eip
                if (product.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ChangeEipModel eipModel = changeGcsModel.getEipModel();
                    if (Objects.nonNull(eipModel)) {
                        ResChangeReqModel.ProductOrder eipProductOrder = new ResChangeReqModel.ProductOrder();
                        eipProductOrder.setGId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderType("EIP_MODIFY");
                        eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
                        eipProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                        ResChangeReqModel.Attrs eipAttrs = new ResChangeReqModel.Attrs();
                        eipAttrs.setResourceId(eipModel.getEipId());
                        eipAttrs.setBandwidth(eipModel.getChangeBandwidth());
                        eipProductOrder.setAttrs(eipAttrs);
                        reqProductList.add(eipProductOrder);
                    }
                }
            }
            //调用任务中心变更资源
            resChangeReqModel.setProductOrders(reqProductList);
            log.info("资源变更，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resChangeReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源变更失败，callLayoutOrder--编排中心初始化返回结果失败");
            log.info("资源变更，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
        }
        //把对应的产品都改成回收中状态
        List<Long> ids = changeWorkOrderProducts.stream().map(External2ChangeOrderProductDTO::getId).collect(Collectors.toList());
        changeWorkOrderProductManager.updateStatusByIds(ids, String.valueOf(ChangeTypeProductStatusEnum.CHANGING.getCode()));
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.GCS;
    }

    private void baseParamInit(ChangeEcsModel changeGcsModel,
                               ResChangeReqModel resChangeReqModel,
                               External2ChangeOrderProductDTO product,
                               External2ChangeOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(changeGcsModel.getBillId(), changeGcsModel.getRegionCode());
        //设置计费号
        resChangeReqModel.setAccount(changeGcsModel.getBillId());
        //设置业务code;
        resChangeReqModel.setSourceExtType(OrderTypeEnum.EXTERNAL2_CHANGE.getCode());
        //设置业务code
        resChangeReqModel.setBusinessCode("ECS_COMBINATION_MODIFY");
        //设置客户id
        resChangeReqModel.setCustomId(changeGcsModel.getCustomNo());
        //设置区域编码
        resChangeReqModel.setRegionCode(changeGcsModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resChangeReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resChangeReqModel.setTenantId(tenantId);
        //设置userId
        resChangeReqModel.setUserId(dto.getCreatorId());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resChangeReqModel.setTaskSource(8);
    }
}
