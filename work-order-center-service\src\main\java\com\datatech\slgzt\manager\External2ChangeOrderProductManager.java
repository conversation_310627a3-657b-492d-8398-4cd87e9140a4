package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.query.External2ChangeOrderProductQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 外部2变更工单产品Manager接口
 */
public interface External2ChangeOrderProductManager {

    /**
     * 新增
     */
    void add(External2ChangeOrderProductDTO dto);

    /**
     * 更新
     */
    void update(External2ChangeOrderProductDTO dto);

    /**
     * 删除
     */
    void delete(Long id);

    /**
     * 根据ID查询
     */
    External2ChangeOrderProductDTO getById(Long id);

    /**
     * 分页查询
     */
    PageResult<External2ChangeOrderProductDTO> page(External2ChangeOrderProductQuery query);

    /**
     * 列表查询
     */
    List<External2ChangeOrderProductDTO> list(External2ChangeOrderProductQuery query);

    /**
     * 批量更新状态
     */
    void updateStatusByIds(List<Long> ids, String status);

    External2ChangeOrderProductDTO getBySubOrderId(Long subOrderId);
}
