package com.datatech.slgzt.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.HAndoverStatusEnum;
import com.datatech.slgzt.model.change.*;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.change.ChangeAuditWorkOrderDTO;
import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.opm.External2ChangeOrderCreateOpm;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.model.req.change.ChangeWorkOrderAuditReq;
import com.datatech.slgzt.model.req.external2.change.External2ChangeOrderCreateReq;
import com.datatech.slgzt.model.req.external2.change.External2ChangeOrderPageReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.change.ChangeWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.external2.External2ChangeOrderVO;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import org.apache.commons.compress.utils.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface External2ChangeOrderWebConvert {

    External2ChangeOrderCreateOpm convert(External2ChangeOrderCreateReq changeWorkOrderCreateOpmDTO);

    External2ChangeOrderVO dto2vo(External2ChangeOrderDTO orderDTO);

    External2ChangeOrderQuery pageReq2Query(External2ChangeOrderPageReq req);

    ChangeAuditWorkOrderDTO req2DTO(ChangeWorkOrderAuditReq req);

    @Named("resourceApplyFiles")
    default String resourceApplyFile(List<UploadFileModel> fileModels) {
        return JSON.toJSONString(fileModels);
    }

    @Named("resourceApplyFiles")
    default List<UploadFileModel> resourceApplyFile(String fileModels) {
        return JSON.parseArray(fileModels, UploadFileModel.class);
    }

    @Mapping(target = "orderType", defaultValue = "orderType", qualifiedByName = "orderType")
    ChangeWorkOrderDetailVO convertDetail(External2ChangeOrderDTO dto);

    @Named("orderType")
    default String orderType(String orderType) {
        return "资源变更";
    }

    default void fillProductDetail(ChangeWorkOrderDetailVO vo,
                                   List<ResourceDetailDTO> resourceDetailDTOS,
                                   ArrayListMultimap<String, External2ChangeOrderProductDTO> type2product,
                                   Long createdBy,
                                   UserCenterUserDTO currentUser) {
        //resourceDetailDTOS 变成 ArrayListMultimap 用来填充对象
        ArrayListMultimap<String, ResourceDetailDTO> type2ResDeatil = StreamUtils.toArrayListMultimap(resourceDetailDTOS, ResourceDetailDTO::getType);

        Set<String> roleCodeSet = currentUser.getOacRoles().stream()
                .map(UserCenterRoleDTO::getCode)
                .collect(Collectors.toSet());

        //设置工单部分信息
        //-------------------------ecs-------------------------------------------
        List<ChangeEcsModel> ecsChangeList = Lists.newArrayList();
        type2product.get("ecs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEcsModel changeEcsModel = JSON.parseObject(propertySnapshot, ChangeEcsModel.class);
            commonFill(type2ResDeatil.get("ecs"), productDTO, changeEcsModel);
            fillEcs(changeEcsModel, createdBy, currentUser, roleCodeSet);
            ecsChangeList.add(changeEcsModel);
        });
        vo.setEcsChangeList(ecsChangeList);
        //-------------------------gcs-------------------------------------------
        List<ChangeEcsModel> gcsChangeList = Lists.newArrayList();
        type2product.get("gcs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEcsModel changeEcsModel = JSON.parseObject(propertySnapshot, ChangeEcsModel.class);
            commonFill(type2ResDeatil.get("gcs"), productDTO, changeEcsModel);
            fillEcs(changeEcsModel, createdBy, currentUser, roleCodeSet);
            gcsChangeList.add(changeEcsModel);
        });
        vo.setGcsChangeList(gcsChangeList);
        //-------------------------evs-------------------------------------------
        List<ChangeEvsModel> evsChangeList = Lists.newArrayList();
        type2product.get("evs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEvsModel changeEvsModel = JSON.parseObject(propertySnapshot, ChangeEvsModel.class);
            commonFill(type2ResDeatil.get("evs"), productDTO, changeEvsModel);
            evsChangeList.add(changeEvsModel);
        });
        vo.setEvsChangeList(evsChangeList);
        //-------------------------共享evs-------------------------------------------
        List<ChangeEvsModel> shareEvsChangeList = Lists.newArrayList();
        type2product.get("shareEvs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEvsModel changeEvsModel = JSON.parseObject(propertySnapshot, ChangeEvsModel.class);
            commonFill(type2ResDeatil.get("shareEvs"), productDTO, changeEvsModel);
            shareEvsChangeList.add(changeEvsModel);
        });
        vo.setShareEvsChangeList(shareEvsChangeList);
        //-------------------------eip-------------------------------------------
        List<ChangeEipProductModel> eipChangeList = Lists.newArrayList();
        type2product.get("eip").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEipProductModel changeEipModel = JSON.parseObject(propertySnapshot, ChangeEipProductModel.class);
            commonFill(type2ResDeatil.get("eip"), productDTO, changeEipModel);
            eipChangeList.add(changeEipModel);
        });
        vo.setEipChangeList(eipChangeList);
        //-------------------------slb-------------------------------------------
        List<ChangeSlbModel> slbChangeList = Lists.newArrayList();
        type2product.get("slb").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeSlbModel changeSlbModel = JSON.parseObject(propertySnapshot, ChangeSlbModel.class);
            commonFill(type2ResDeatil.get("slb"), productDTO, changeSlbModel);
            slbChangeList.add(changeSlbModel);
        });
        vo.setSlbChangeList(slbChangeList);
        //-------------------------obs-------------------------------------------
        List<ChangeObsModel> obsListExt = Lists.newArrayList();
        type2product.get("obs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeObsModel changeObsModel = JSON.parseObject(propertySnapshot, ChangeObsModel.class);
            commonFill(type2ResDeatil.get("obs"), productDTO, changeObsModel);
            obsListExt.add(changeObsModel);
        });
        vo.setObsChangeList(obsListExt);
        //-------------------------nat-------------------------------------------
        List<ChangeNatModel> natChangeList = Lists.newArrayList();
        type2product.get("nat").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeNatModel changeNatModel = JSON.parseObject(propertySnapshot, ChangeNatModel.class);
            commonFill(type2ResDeatil.get("nat"), productDTO, changeNatModel);
            natChangeList.add(changeNatModel);
        });
        //-------------------------nat-------------------------------------------
        List<ChangeEcsModel> redisChangeList = Lists.newArrayList();
        type2product.get("redis").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEcsModel changeEcsModel = JSON.parseObject(propertySnapshot, ChangeEcsModel.class);
            commonFill(type2ResDeatil.get("redis"), productDTO, changeEcsModel);
            fillEcs(changeEcsModel, createdBy, currentUser, roleCodeSet);
            redisChangeList.add(changeEcsModel);
        });
        vo.setRedisChangeList(redisChangeList);
        //-------------------------nat-------------------------------------------
        List<ChangeEcsModel> mysqlChangeList = Lists.newArrayList();
        type2product.get("mysql").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeEcsModel changeEcsModel = JSON.parseObject(propertySnapshot, ChangeEcsModel.class);
            commonFill(type2ResDeatil.get("mysql"), productDTO, changeEcsModel);
            fillEcs(changeEcsModel, createdBy, currentUser, roleCodeSet);
            mysqlChangeList.add(changeEcsModel);
        });
        vo.setMysqlChangeList(mysqlChangeList);
        //-------------------------rdsMysql----------------------------------------
        List<ChangeMysqlModel> rdsMysqlChangeList = Lists.newArrayList();
        type2product.get("rdsMysql").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            ChangeMysqlModel changeMysqlModel = JSON.parseObject(propertySnapshot, ChangeMysqlModel.class);
            commonFill(type2ResDeatil.get("rdsMysql"), productDTO, changeMysqlModel);
            rdsMysqlChangeList.add(changeMysqlModel);
        });
        vo.setRdsMysqlChangeList(rdsMysqlChangeList);
    }

    default void fillEcs(ChangeEcsModel changeEcsModel, Long createdBy, UserCenterUserDTO currentUser, Set<String> roleCodeSet) {
        if ("RUNING".equals(changeEcsModel.getDeviceStatus()) && ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode().equals(changeEcsModel.getChangeStatus())) {
            changeEcsModel.setMessage(null);
        }
        if (changeEcsModel.getHandoverStatus() == null
                || changeEcsModel.getHandoverStatus().equals(HAndoverStatusEnum.NO.getType())) {
            // 未交维
            if (currentUser.getId().equals(createdBy) || roleCodeSet.contains(AuthorityCodeEnum.OPERATION_GROUP.code())) {
                changeEcsModel.setCanClose(true);
                return;
            }
        } else {
            // 已交维
            if (currentUser.getId().equals(createdBy)) {
                changeEcsModel.setCanClose(true);
                return;
            }
        }
        changeEcsModel.setCanClose(false);
    }


    default void commonFill(List<ResourceDetailDTO> details, External2ChangeOrderProductDTO productDTO, ChangeBaseModel model) {
        model.setTenantConfirm(productDTO.getTenantConfirm());
        model.setId(productDTO.getId());
        model.setChangeStatus(productDTO.getChangeStatus());
        model.setBlockWarning(productDTO.getBlockWarning());
        model.setProductType(productDTO.getProductType());
        model.setMessage(productDTO.getMessage());
        model.setNewExpireTime(DateUtils.processGoodsExpireTime(model.getExpireTime(), model.getChangeTime()));
        if (CollectionUtil.isNotEmpty(details)) {
            details.forEach(detail -> {
                if (detail.getId().equals(Long.parseLong(productDTO.getResourceDetailId()))) {
                    model.setDeviceStatus(detail.getDeviceStatus());
                    //model.setExpireTime(detail.getExpireTime());
                    if (model.getHandoverStatus() == null) {
                        // 工单完成后，交维状态会存档保存，不再随detail变化
                        model.setHandoverStatus(detail.getHandoverStatus());
                    }
                }
            });
        }
    }

    default void mergeSchemaInfo(ChangeWorkOrderDetailVO vo, ChangeAuditWorkOrderDTO schemaInfo) {
        vo.setCloudArchitecture(schemaInfo.getCloudArchitecture());
        vo.setCloudResources(schemaInfo.getCloudResources());
        vo.setBusinessArchitecture(schemaInfo.getBusinessArchitecture());
        vo.setBusinessPlanning(schemaInfo.getBusinessPlanning());
    }
}
