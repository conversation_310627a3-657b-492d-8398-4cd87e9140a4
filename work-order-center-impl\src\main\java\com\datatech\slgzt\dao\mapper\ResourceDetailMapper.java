package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.model.dto.ModuleOfflineDTO;
import com.datatech.slgzt.model.dto.OperateVmDTO;
import com.datatech.slgzt.model.dto.ResetVmPwdDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.query.VmOperateQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ResourceDetailMapper extends BaseMapper<ResourceDetailDO> {

    ResourceDetailDO selectByIdNoStatus(@Param("id") long id);

    void updateConfigId(@Param("configId") String configId, @Param("id") Long id, @Param("manageIp") String manageIp);

    List<ResourceDetailDO> selectOrderGoodsByType(@Param("businessIds") List<String> businessIds,
                                                  @Param("productTypes") List<String> productTypes,
                                                  @Param("recoveryType") Integer recoveryType);

    NetcardDetailDTO selectNetInfoByUuidAndIp(@Param("instanceUuid") String instanceUuid, @Param("ip") String ip);

    ResetVmPwdDTO selectOperateVmResetPwd(@Param("operate") VmOperateQuery operate);

    OperateVmDTO selectOperateVm(@Param("operate") VmOperateQuery query);

    void updateDeviceStatusByDeviceId(@Param("deviceStatus") String deviceStatus, @Param("deviceId") String deviceId);

    List<ResourceDetailDO> selectRecoveryStatusByIds(@Param("ids") List<String> ids, @Param("recoveryStatus") Integer recoveryStatus, @Param("status") Integer status);

    List<String> selectRegionCodeByOrderId(@Param("orderId") String orderId);

    List<ModuleOfflineDTO> selectNotUserModule(@Param("businessSysId") Long businessSysId);

    void updateChangeStatusByIds(@Param("ids") List<Long> ids, @Param("changeStatus") String changeStatus);
    void updateChangeStatusByDeviceIds(@Param("ids") List<String> ids, @Param("changeStatus") String changeStatus);

    void updateHandoverStatusByConfigId(@Param("configId") String configId, @Param("handoverStatus") String handoverStatus);

    List<ResourceDetailDO> selectByExpireTime(@Param("time") String time,@Param("endTime") String endTime);
    List<ResourceDetailDO> selectByExpireTimeThreeDay();
    List<ResourceDetailDO> selectExpireDetail();

    ResourceDetailDTO selectResourceDetailOfObs(@Param("id") Long id);

}
