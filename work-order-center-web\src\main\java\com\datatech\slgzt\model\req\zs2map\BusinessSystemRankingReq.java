package com.datatech.slgzt.model.req.zs2map;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 业务系统利用率排行查询请求
 */
@Data
public class BusinessSystemRankingReq {
    
    /**
     * 区域编码（可选）
     */
    private String areaCode;
    
    /**
     * 卡类型/显卡型号（可选）
     */
    private String modelName;
    
    /**
     * 开始时间（必填）
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间（必填）
     */
    private LocalDateTime endTime;
}
