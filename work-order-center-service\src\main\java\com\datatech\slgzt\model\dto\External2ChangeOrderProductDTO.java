package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部2变更工单产品DTO
 */
@Data
public class External2ChangeOrderProductDTO {

    /** 产品ID */
    private Long id;

    /** 工单ID */
    private String workOrderId;

    /** 创建的工单ID */
    private String createWorkOrderId;

    /** 更变类型 */
    private List<String> changeType;

    /** 产品类型 */
    private String productType;

    /** 属性快照 */
    private String propertySnapshot;

    /** 父类产品ID */
    private Long parentProductId;

    /** GID */
    private String gid;

    /** 子订单ID */
    private Long subOrderId;

    /** 开通状态 */
    private String changeStatus;

    /** 消息 */
    private String message;

    /** 是否启用 */
    private Boolean enabled;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改时间 */
    private LocalDateTime modifyTime;

    /** 资源详情ID */
    private String resourceDetailId;

    /** 屏蔽告警 */
    private Boolean blockWarning;

    /** 租户确认 */
    private Boolean tenantConfirm;

    /** 串行变更状态 */
    private Integer serialChangeStatus;
}
