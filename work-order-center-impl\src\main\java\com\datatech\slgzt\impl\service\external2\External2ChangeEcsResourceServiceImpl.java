package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.change.ChangeEcsModel;
import com.datatech.slgzt.model.change.ChangeEipModel;
import com.datatech.slgzt.model.change.ChangeEvsModel;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.layout.ResChangeReqModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2ChangeResourceService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: 云主机规格变更
 * @author: LK
 * @create: 2025-04-08 17:15
 **/
@Service
@Slf4j
public class External2ChangeEcsResourceServiceImpl implements External2ChangeResourceService {

    @Resource
    private PlatformService platformService;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    @Resource
    private External2ChangeOrderProductManager changeWorkOrderProductManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void changeResource(External2ChangeOrderDTO dto, List<External2ChangeOrderProductDTO> changeWorkOrderProducts) {
        for (External2ChangeOrderProductDTO product : changeWorkOrderProducts) {
            if (ProductTypeEnum.ECS.getCode().equals(product.getProductType())) {
                ResChangeReqModel resChangeReqModel = new ResChangeReqModel();
                //参数封装
                List<ResChangeReqModel.ProductOrder> reqProductList = Lists.newArrayList();
                ChangeEcsModel changeEcsModel = JSONObject.parseObject(product.getPropertySnapshot(), ChangeEcsModel.class);
                //基础参数封装
                baseParamInit(changeEcsModel, resChangeReqModel, product, dto);
                //规格变更封装云主机
                if (product.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    ResChangeReqModel.ProductOrder ecsProductOrder = new ResChangeReqModel.ProductOrder();
                    ecsProductOrder.setGId(changeEcsModel.getProductOrderId().toString());
                    ecsProductOrder.setProductOrderId(changeEcsModel.getProductOrderId().toString());
                    ecsProductOrder.setProductOrderType("ECS_MODIFY");
                    ecsProductOrder.setProductType(ProductTypeEnum.ECS.getCode());
                    ecsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                    ResChangeReqModel.Attrs ecsAttrs = new ResChangeReqModel.Attrs();
                    ecsAttrs.setResourceId(changeEcsModel.getVmId());
                    ecsAttrs.setFlavorId(changeEcsModel.getChangeFlavorId());
                    ecsAttrs.setFlavorCode(changeEcsModel.getChangeFlavorId());
                    ecsProductOrder.setAttrs(ecsAttrs);
                    reqProductList.add(ecsProductOrder);
                }
                //存储变更封装云硬盘
                if (product.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    List<ChangeEvsModel> evsModelList = changeEcsModel.getEvsModelList();
                    if (CollectionUtil.isNotEmpty(evsModelList)) {
                        evsModelList.forEach(evs -> {
                            ResChangeReqModel.ProductOrder evsProductOrder = new ResChangeReqModel.ProductOrder();
                            evsProductOrder.setGId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderType("EVS_MODIFY");
                            evsProductOrder.setProductType(ProductTypeEnum.EVS.getCode());
                            evsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                            ResChangeReqModel.Attrs evsAttrs = new ResChangeReqModel.Attrs();
                            evsAttrs.setResourceId(evs.getEvsId());
                            evsAttrs.setVolumeSize(evs.getChangeVolumeSize());
                            evsProductOrder.setAttrs(evsAttrs);
                            reqProductList.add(evsProductOrder);
                        });
                    }
                }
                //带宽变更封装eip
                if (product.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ChangeEipModel eipModel = changeEcsModel.getEipModel();
                    if (Objects.nonNull(eipModel)) {
                        ResChangeReqModel.ProductOrder eipProductOrder = new ResChangeReqModel.ProductOrder();
                        eipProductOrder.setGId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderType("EIP_MODIFY");
                        eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
                        eipProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                        ResChangeReqModel.Attrs eipAttrs = new ResChangeReqModel.Attrs();
                        eipAttrs.setResourceId(eipModel.getEipId());
                        eipAttrs.setBandwidth(eipModel.getChangeBandwidth());
                        eipProductOrder.setAttrs(eipAttrs);
                        reqProductList.add(eipProductOrder);
                    }
                }
                //调用任务中心变更资源
                resChangeReqModel.setProductOrders(reqProductList);
                log.info("资源变更，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
                Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                        .bodyType(OkHttps.JSON)
                        .setBodyPara(JSON.toJSONString(resChangeReqModel))
                        .post()
                        .getBody()
                        .toMapper();
                String success = dataMapper.getString("success");
                Precondition.checkArgument("1".equals(success), "资源变更失败，callLayoutOrder--编排中心初始化返回结果失败");
                log.info("资源变更，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
            }
            //把对应的产品都改成回收中状态
            List<Long> ids = changeWorkOrderProducts.stream().map(External2ChangeOrderProductDTO::getId).collect(Collectors.toList());
            changeWorkOrderProductManager.updateStatusByIds(ids, String.valueOf(ChangeTypeProductStatusEnum.CHANGING.getCode()));
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.ECS;
    }

    private void baseParamInit(ChangeEcsModel changeEcsModel,
                               ResChangeReqModel resChangeReqModel,
                               External2ChangeOrderProductDTO product,
                               External2ChangeOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(changeEcsModel.getBillId(), changeEcsModel.getRegionCode());
        //设置计费号
        resChangeReqModel.setAccount(changeEcsModel.getBillId());
        //设置业务code;
        resChangeReqModel.setSourceExtType(OrderTypeEnum.EXTERNAL2_CHANGE.getCode());
        //设置业务code
        resChangeReqModel.setBusinessCode("ECS_COMBINATION_MODIFY");
        //设置客户id
        resChangeReqModel.setCustomId(changeEcsModel.getCustomNo());
        //设置区域编码
        resChangeReqModel.setRegionCode(changeEcsModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resChangeReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resChangeReqModel.setTenantId(tenantId);
        //设置userId
        resChangeReqModel.setUserId(dto.getCreatorId());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resChangeReqModel.setTaskSource(8);
    }

    /**
     * 延期处理
     *
     * @param product
     */
    private void dealDelayOrder(ChangeWorkOrderProductDTO product, ChangeEcsModel changeEcsModel) {
        ResourceDetailDTO detailDTO = resourceDetailManager.getById(Long.valueOf(product.getResourceDetailId()));
        //按照时间更新
        ResourceDetailDTO updateDTO = new ResourceDetailDTO();
        updateDTO.setId(detailDTO.getId());
        updateDTO.setExpireTime(DateUtils.processGoodsExpireTime(detailDTO.getExpireTime(), changeEcsModel.getChangeTime()));
        resourceDetailManager.updateById(updateDTO);
        //如果detailDTO.getEip不为空同样要更新
        if (ObjNullUtils.isNotNull(detailDTO.getEip())) {
            ResourceDetailDTO eipDTO = resourceDetailManager.getById(Long.valueOf(product.getResourceDetailId()));
            updateDTO.setId(eipDTO.getId());
            updateDTO.setExpireTime(DateUtils.processGoodsExpireTime(eipDTO.getExpireTime(), changeEcsModel.getChangeTime()));
            resourceDetailManager.updateById(updateDTO);
        }
        //如果detailDTO.getEvs不为空同样要更新
        if (ObjNullUtils.isNotNull(detailDTO.getVolumeId())) {
            for (String vId : detailDTO.getVolumeId().split(",")) {
                ResourceDetailDTO evsDTO = resourceDetailManager.getByDeviceId(vId);
                updateDTO.setId(evsDTO.getId());
                updateDTO.setExpireTime(DateUtils.processGoodsExpireTime(evsDTO.getExpireTime(), changeEcsModel.getChangeTime()));
                resourceDetailManager.updateById(updateDTO);
            }
        }
    }

    /**
     * 变更处理
     *
     * @param product
     * @param dto
     */
    private void dealChangeOrder(ChangeWorkOrderProductDTO product, ChangeWorkOrderDTO dto) {

    }
}
