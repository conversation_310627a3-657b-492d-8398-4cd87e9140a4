package com.datatech.slgzt.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datatech.slgzt.dao.mapper.DeviceCardMetricsMapper;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import jodd.util.StringUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@DS("clickhouse")
@Repository
public class DeviceCardMetricsDAO extends ServiceImpl<DeviceCardMetricsMapper, DeviceCardMetricsDO> {
    @Resource
    private DeviceCardMetricsMapper mapper;

    public DeviceCardMetricsDO getById(String id) {
        return getById(id);
    }

    public void insert(DeviceCardMetricsDO deviceCardMetricsDO) {
        save(deviceCardMetricsDO);
    }

    public int insertBatch(List<DeviceCardMetricsDO> deviceMetrics){
          saveBatch(deviceMetrics);
          return 0;

    }

    public boolean updateById(DeviceCardMetricsDO deviceCardMetricsDO) {
        mapper.updateById(deviceCardMetricsDO);
        return false;
    }

    public void deleteById(Long id) {
        removeById(id);
    }

    public void deleteByIds(List<Long> ids) {
        removeByIds(ids);
    }

    public void delByDataTime(String dataTime) {
        remove(Wrappers.<DeviceCardMetricsDO>lambdaQuery()
                .eq(DeviceCardMetricsDO::getGpuTime,dataTime));
    }


    /**
     * 查询所有指标数据
     * @return
     */
    public List<DeviceCardMetricsDO> selectDeviceMetric(String gpuTime){
        LambdaQueryWrapper<DeviceCardMetricsDO> queryWrapper = Wrappers.<DeviceCardMetricsDO>lambdaQuery()
                .like(StringUtils.isNotBlank(gpuTime),DeviceCardMetricsDO::getGpuTime,gpuTime);
        return list(queryWrapper);
    }

    /**
     * 查询所有指标数据
     * @param deviceMetricQuery
     * @return
     */
    public List<DeviceCardMetricsDO> selectDeviceMetric(DeviceMetricQuery deviceMetricQuery){
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getStartTime())){
            deviceMetricQuery.setStartTime(deviceMetricQuery.getStartTime().withNano(0));
        }
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getEndTime())) {
            deviceMetricQuery.setEndTime(deviceMetricQuery.getEndTime().withNano(0));
        }

        LambdaQueryWrapper<DeviceCardMetricsDO> queryWrapper = Wrappers.<DeviceCardMetricsDO>lambdaQuery()
                .like(StringUtil.isNotBlank(deviceMetricQuery.getDeviceId()), DeviceCardMetricsDO::getDeviceId, deviceMetricQuery.getDeviceId())
                .in(CollectionUtils.isNotEmpty(deviceMetricQuery.getDeviceIds()), DeviceCardMetricsDO::getDeviceId, deviceMetricQuery.getDeviceIds())
                .like(StringUtil.isNotBlank(deviceMetricQuery.getModelName()), DeviceCardMetricsDO::getModelName, deviceMetricQuery.getModelName())
                .eq(StringUtil.isNotBlank(deviceMetricQuery.getGpuTime()), DeviceCardMetricsDO::getGpuTime, deviceMetricQuery.getGpuTime())
                .in(CollectionUtils.isNotEmpty(deviceMetricQuery.getMetricSources()), DeviceCardMetricsDO::getMetricSource, deviceMetricQuery.getMetricSources())
                .eq(StringUtil.isNotBlank(deviceMetricQuery.getDeviceType()), DeviceCardMetricsDO::getDeviceType, deviceMetricQuery.getDeviceType())
                .eq(StringUtil.isNotBlank(deviceMetricQuery.getAreaCode()), DeviceCardMetricsDO::getAreaCode, deviceMetricQuery.getAreaCode())
                .ge(ObjNullUtils.isNotNull(deviceMetricQuery.getStartTime()), DeviceCardMetricsDO::getCreatedAt, deviceMetricQuery.getStartTime())
                .le(ObjNullUtils.isNotNull(deviceMetricQuery.getEndTime()), DeviceCardMetricsDO::getCreatedAt, deviceMetricQuery.getEndTime())
                .orderByDesc(DeviceCardMetricsDO::getCreatedAt);
        return list(queryWrapper);
    }

    public List<DeviceCardMetricsDO> queryAvgDeviceMetrics(DeviceMetricQuery deviceMetricQuery) {
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getStartTime())){
            deviceMetricQuery.setStartTime(deviceMetricQuery.getStartTime().withNano(0));
        }
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getEndTime())) {
            deviceMetricQuery.setEndTime(deviceMetricQuery.getEndTime().withNano(0));
        }
        return mapper.queryAvgDeviceMetrics(deviceMetricQuery.getAreaCode(),
                deviceMetricQuery.getStartTime(),
                deviceMetricQuery.getEndTime());
    }

    /**
     * 按时间聚合查询GPU指标数据（数据库层面聚合，提升性能）
     */
    public List<DeviceCardMetricsDO> queryGpuMetricsAggregated(List<String> deviceIds, 
                                                             DeviceMetricQuery deviceMetricQuery) {
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getStartTime())){
            deviceMetricQuery.setStartTime(deviceMetricQuery.getStartTime().withNano(0));
        }
        if (ObjNullUtils.isNotNull(deviceMetricQuery.getEndTime())) {
            deviceMetricQuery.setEndTime(deviceMetricQuery.getEndTime().withNano(0));
        }
        
        return mapper.queryGpuMetricsAggregated(
                deviceIds,
                deviceMetricQuery.getStartTime(),
                deviceMetricQuery.getEndTime(),
                deviceMetricQuery.getStatType()
        );
    }

    /**
     * 按区域和卡类型查询利用率聚合数据
     * @param areaCode 区域编码（可以为空）
     * @param modelName 卡类型（必须）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param aggregationType 聚合类型（day/month）
     * @return 聚合后的利用率数据
     */
    public List<DeviceCardMetricsDO> queryCardUtilizationMetrics(String areaCode, String modelName,
                                                               java.time.LocalDateTime startTime, java.time.LocalDateTime endTime,
                                                               String aggregationType) {
        if (ObjNullUtils.isNotNull(startTime)){
            startTime = startTime.withNano(0);
        }
        if (ObjNullUtils.isNotNull(endTime)) {
            endTime = endTime.withNano(0);
        }
        
        return mapper.queryCardUtilizationMetrics(areaCode, modelName, startTime, endTime, aggregationType);
    }



    /**
     * 按区域和卡类型查询利用率聚合数据
     * @param areaCode 区域编码（可以为空）
     * @param modelName 卡类型（必须）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param aggregationType 聚合类型（day/month）
     * @return 聚合后的利用率数据
     */
    public List<DeviceCardMetricsDO> queryCardUtilizationMetrics2(String areaCode, String modelName,
                                                                 java.time.LocalDateTime startTime, java.time.LocalDateTime endTime,
                                                                 String aggregationType) {
        if (ObjNullUtils.isNotNull(startTime)){
            startTime = startTime.withNano(0);
        }
        if (ObjNullUtils.isNotNull(endTime)) {
            endTime = endTime.withNano(0);
        }

        return mapper.queryCardUtilizationMetrics2(areaCode, modelName, startTime, endTime, aggregationType);
    }

    /**
     * 查询业务系统历史利用率数据（按时间范围聚合）
     * @param areaCode 区域编码（可选）
     * @param modelName 卡类型（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 按业务系统聚合的历史利用率数据
     */
    public List<DeviceCardMetricsDO> queryBusinessSystemHistoryMetrics(String areaCode, String modelName,
                                                                      java.time.LocalDateTime startTime, 
                                                                      java.time.LocalDateTime endTime) {
        if (ObjNullUtils.isNotNull(startTime)){
            startTime = startTime.withNano(0);
        }
        if (ObjNullUtils.isNotNull(endTime)) {
            endTime = endTime.withNano(0);
        }
        
        return mapper.queryBusinessSystemHistoryMetrics(areaCode, modelName, startTime, endTime);
    }

}
