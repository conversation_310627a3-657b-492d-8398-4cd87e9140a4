package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * @program: cloudbasedbusinessproject
 * @description: 商品单资源详情实体类
 * @author: LK
 * @create: 2024-12-25 13:44
 **/
@Data
public class ResourceDetailDTO extends ExtendResourceDetail {

    private Long id;

    /**
     * 商品订单id
     */
    private Long goodsOrderId;

    /**
     * 拓展字段
     */
    private String sourceExtType;

    /**
     * 资源类型
     */
    private String type;
//
//    /**
//     * 资源类型
//     */
//    private String deviceType;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单类型
     * @see OrderTypeEnum
     */
    private String orderType;


    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 资源id
     */
    private String resourceId;


    /**
     * azCode
     */
    private String azCode;

    /**
     * azName
     */
    private String azName;

    /**
     * azId
     */
    private String azId;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * 规格
     */
    private String spec;

    /**
     * 系统盘
     */
    private String sysDisk;

    /**
     * 数据盘
     */
    private String dataDisk;

    /**
     * 数据盘id
     */
    private String volumeId;

    /**
     * 共享数据盘Id
     */
    private String shareVolumeId;

    /**
     * 共享数据盘
     */
    private String shareDataDisk;

    /**
     * ip
     */
    private String ip;

    /**
     * 弹性ip
     */
    private String eip;

    /**
     * 弹性ip id
     */
    private String eipId;

    /**
     * 带宽
     */
    private String bandWidth;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 业务系统id
     */
    private Long businessSysId;

    /**
     * 业务系统名称
     */
    private String businessSysName;

    /**
     * 云平台
     */
    private String cloudPlatform;

    /**
     * 资源池id
     */
    private String resourcePoolId;

    /**
     * 资源池名称
     */
    private String resourcePoolName;

    /**
     * 资源池code
     */
    private String resourcePoolCode;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 资源申请时间
     */
    private LocalDateTime resourceApplyTime;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 设备状态
     */
    private String deviceStatus;

    /**
     * 申请用户id
     */
    private Long applyUserId;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * vpc
     */
    private String vpcId;

    /**
     * vpc
     */
    private String vpcName;

    /**
     * 子网
     */
    private String subnetId;

    /**
     * 子网
     */
    private String subnetName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 挂载云主机Id
     */
    private String vmId;

    /**
     * 挂载主机名称(IP)
     */
    private String ecsName;

    /**
     * 是否挂载云主机，true：是，false：否
     */
    private String mountOrNot;

    /**
     * 实例uuid
     */
    private String instanceUuid;

    /**
     * 网卡id
     */
    private String netId;

    /**
     * 网络名称
     */
    private String netName;

    /**
     * mac地址
     */
    private String mac;

    /**
     * 配置项id
     */
    private String configId;

    /**
     *
     */
    private Integer recoveryStatus;

    /**
     * 退维状态
     *
     * @see DisDimensionStatusEnum
     */
    private String disDimensionStatus;

    /**
     * ====obs字段====
     */
    /**
     * 公钥
     */
    private String accessKey;

    /**
     * 私钥
     */
    private String secretKey;

    /**
     * 公网地址
     */
    private String publicAddress;

    /**
     * 私网地址
     */
    private String internalAddress;
    /**
     * 私网地址
     */
    private String manageIp;

    /**
     * 计费号
     */
    private String billId;

    //-----obs规格拆分字段--------
    /**
     * 存储类型（obs）
     */
    private String storeType;

    /**
     * 容量（obs）
     */
    private String capacity;
    //-----obs规格拆分字段--------

    /**
     * 回收状态描述
     */
    private String recoveryStatusCn;

    /**
     * 允许重置密码：1允许，0不允许
     */
    private Integer resetPwd;

    /**
     * 退维状态：success-退维成功，running-退维中，fail-退维失败,null-不展示状态
     */
    private String disDimensionStatusCn;


    /**
     * 回收信息
     */
    private String message;

    private Integer month;


    /**
     * 域名
     */
    private String domainName;


    private String domainCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 绑定的网络模型快照
     */
    private String networkModelSnapshot;

    /**
     * 来源
     */
    private String sourceType;

    private Long moduleId;

    private String moduleName;

    /**
     * 变更状态
     */
    private String changeStatus;

    /**
     * 变更状态中文
     */
    private String changeStatusCn;

    /**
     * 交维状态
     */
    private String handoverStatus;
    /**
     * 关联设备id
     */
    private String relatedDeviceId;
    /**
     * 关联设备名称
     */
    private String relatedDeviceName;
    /**
     * 关联设备类型
     */
    private String relatedDeviceType;
    /**
     * 历史变更记录，变更单id，逗号分隔
     */
    private String hisChangeOrderIds;
    /**
     * 历史变更记录，变更单编号，逗号分隔
     */
    private String hisChangeOrderCodes;

    /**
     * 安全组id
     */
    private String securityGroupIds;

    private String securityGroupName;

    /**
     * 虚拟网卡id（多个）
     */
    private String vnicId;

    /**
     * 虚拟网卡名称
     */
    private String vnicName;

    /**
     * 是否需要同步回收
     */
    private Boolean syncRecovery;

    //-----备份策略------
    /**
     * 备份类型 ECS：云主机 EVS：云硬盘
     */
    private String backupType;

    /**
     * 备份频率 weeks/days
     * 若type=“days”时填写，表示每天都备份执行
     */
    private String frequency;

    /**
     * 周数
     * 若type=“weeks”时填写：每周几执行，1-7分别代表周一到周日
     */
    private Integer daysOfWeek;

    /**
     * 云备份策略id
     */
    private String backupId;


    //计费类型 day:按天计费；month:按月计费
    private String billType;

    //计费方式 quant：按量计费；require：按需计费
    private String chargeType;

    /**
     * 数据存储总量
     */
    private String dataStorageTotal;


    private String gpuCardType;

    private String gpuType;

    private Integer gpuNum;

    //退订状态
    private String disOrderStatus;

    private String username;

    private String password;

    private String ipVersion;

    /**
     * 是否支持ipv6的eip(云主机)
     */
    private Boolean ipv6Enable;

    /**
     * ============
     * 与obs资源池联查字段
     */
    private String clusterName;

    private String storagePoolName;

    private String endpoint;

}
