package com.datatech.slgzt.service.external2;

import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.opm.External2ChangeOrderCreateOpm;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.concurrent.ExecutionException;

/**
 * 外部2变更工单Service接口
 */
public interface External2ChangeOrderService {

    String createChangeWorkOrder(External2ChangeOrderCreateOpm opm) throws ExecutionException, InterruptedException;

    PageResult<External2ChangeOrderDTO> page(External2ChangeOrderQuery orderQuery, Long userId);


    void checkChangeOrderCanCreate(External2ChangeOrderCreateOpm opm);

    void tryStartEcs(com.datatech.slgzt.model.dto.ResourceDetailDTO detailDTO);

    void restart(String workOrderId);
}
