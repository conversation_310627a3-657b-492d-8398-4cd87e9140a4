package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.query.External2OrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface External2OrderManager {

    List<External2OrderDTO> list(External2OrderQuery query);

    PageResult<External2OrderDTO> page(External2OrderQuery query);

    String insert(External2OrderDTO dto);

    void update(External2OrderDTO dto);

    void delete(String id);

    External2OrderDTO getById(String id);

    External2OrderDTO getByOrderCode(String orderCode);
}
