package com.datatech.slgzt.model.vo.external2;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class External2OrderProductVO {

    private Long id;

    private String workOrderId;

    private String productType;

    private String propertySnapshot;

    private Long parentProductId;

    private String openStatus;

    private String message;

    private String gid;

    private String ext;

    private Long subOrderId;

    private Long jobExecutionId;

    private Boolean enabled;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;
} 