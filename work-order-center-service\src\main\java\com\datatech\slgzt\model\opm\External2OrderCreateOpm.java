package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 外部2工单创建OPM
 */
@Data
public class External2OrderCreateOpm {

    //直接提交订单
    private Boolean directSubmit = Boolean.TRUE;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 租户ID
     */
    private Long tenantId;

    private String tenantName;

    //金额
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 计费号
     */
    private String billId;

    //客户No
    private String customNo;


    //----------------------------------------------------------------------

    /**
     * Ecs申请资源列表的json
     *
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     */
    private List<MysqlV2Model> rdsMysqlModelList;

    private List<EcsModel> redisModelList;

    private List<EcsModel> mysqlModelList;

    private List<EcsModel> postgreSqlModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 云端口
     */
    private List<CloudPortModel> cloudPortModelList;

    /**
     * vpn
     */
    private List<VpnModel> vpnModelList;

    private List<BackupModel> backupModelList;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;
}
