package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2工单产品表
 */
@Data
@TableName("WOC_EXTERNAL2_ORDER_PRODUCT")
public class External2OrderProductDO {

    @TableField("ID")
    private Long id;

    @TableField("ORDER_ID")
    private String orderId;

    @TableField("PRODUCT_TYPE")
    private String productType;

    @TableField("PROPERTY_SNAPSHOT")
    private String propertySnapshot;

    @TableField("PARENT_PRODUCT_ID")
    private Long parentProductId;

    @TableField("OPEN_STATUS")
    private String openStatus;

    @TableField("MESSAGE")
    private String message;

    @TableField("GID")
    private String gid;

    @TableField("EXT")
    private String ext;

    @TableField("SUB_ORDER_ID")
    private Long subOrderId;

    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;

    @TableField("ENABLED")
    @TableLogic(value = "1", delval = "0")
    private Boolean enabled;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;
}
