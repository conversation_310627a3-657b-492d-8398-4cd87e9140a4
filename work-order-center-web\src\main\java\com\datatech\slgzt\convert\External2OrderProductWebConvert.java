package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.datatech.slgzt.model.req.external2.External2OrderProductCreateReq;
import com.datatech.slgzt.model.req.external2.External2OrderProductPageReq;
import com.datatech.slgzt.model.req.external2.External2OrderProductUpdateReq;
import com.datatech.slgzt.model.vo.external2.External2OrderProductVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface External2OrderProductWebConvert {

    External2OrderProductQuery convert(External2OrderProductPageReq req);

    External2OrderProductDTO convert(External2OrderProductCreateReq req);

    External2OrderProductDTO convert(External2OrderProductUpdateReq req);

    External2OrderProductVO convert(External2OrderProductDTO dto);
} 