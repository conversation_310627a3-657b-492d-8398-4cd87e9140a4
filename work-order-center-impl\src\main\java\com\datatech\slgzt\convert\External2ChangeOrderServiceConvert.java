package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.opm.External2ChangeOrderCreateOpm;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface External2ChangeOrderServiceConvert {

    External2ChangeOrderDTO convert(External2ChangeOrderCreateOpm dto);

    @Named("resourceApplyFiles")
    default String resourceApplyFile(List<UploadFileModel> fileModels) {
        return JSON.toJSONString(fileModels);
    }

}
