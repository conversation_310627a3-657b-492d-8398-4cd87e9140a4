package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DeviceGpuInfoMapper extends BaseMapper<DeviceGpuInfoDO> {


    @Select("SELECT DISTINCT model_name FROM WOC_DEVICE_PHYSICAL")
    List<String> groupModelName();

    @Select({
            "<script>",
            "SELECT DISTINCT DEPT_NAME ",
            "FROM WOC_DEVICE_PHYSICAL ",
            "WHERE DEPT_NAME IS NOT NULL ",
            "<if test=\"modelNames != null and modelNames.size > 0\">",
            "  AND (",
            "       MODEL_NAME IN ",
            "       <foreach item='item' collection='modelNames' open='(' separator=',' close=')'>",
            "           #{item}",
            "       </foreach>",
            "       OR ",
            "       SUB_MODEL_NAME IN ",
            "       <foreach item='item' collection='modelNames' open='(' separator=',' close=')'>",
            "           #{item}",
            "       </foreach>",
            "  )",
            "</if>",
            "</script>"
    })
    List<String> groupDeptName(@Param("modelNames") List<String> modelNames);

    @Select({
            "<script>",
            "SELECT DISTINCT BUSINESS_SYSTEM_NAME ",
            "FROM WOC_DEVICE_PHYSICAL ",
            "WHERE BUSINESS_SYSTEM_NAME IS NOT NULL ",
            "<if test=\"modelNames != null and modelNames.size > 0\">",
            "  AND (",
            "       MODEL_NAME IN ",
            "       <foreach item='item' collection='modelNames' open='(' separator=',' close=')'>",
            "           #{item}",
            "       </foreach>",
            "       OR ",
            "       SUB_MODEL_NAME IN ",
            "       <foreach item='item' collection='modelNames' open='(' separator=',' close=')'>",
            "           #{item}",
            "       </foreach>",
            "  )",
            "</if>",
            "</script>"
    })
    List<String> groupBusinessSystemName(@Param("modelNames") List<String> modelNames);
}
