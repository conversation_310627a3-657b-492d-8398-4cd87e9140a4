package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.order.External2ChangeOrderProductDO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;

/**
 * 外部2变更工单产品Manager转换器
 */
@Mapper(componentModel = "spring")
public interface External2ChangeOrderProductManagerConvert {

    /**
     * DTO 转 DO
     */
    @Mapping(target = "changeType", source = "changeType", qualifiedByName = "changeType")
    External2ChangeOrderProductDTO do2dto(External2ChangeOrderProductDO external2ChangeOrderProductDO);

    /**
     * DO 转 DTO
     */
    @Mapping(target = "changeType", source = "changeType", qualifiedByName = "changeType")
    External2ChangeOrderProductDO dto2do(External2ChangeOrderProductDTO external2ChangeOrderProductDTO);

    List<External2ChangeOrderProductDTO> dos2DTOs(List<External2ChangeOrderProductDO> productDOS);

    List<External2ChangeOrderProductDO> dtoList2DOs(List<External2ChangeOrderProductDTO> productDTOS);

    @Named("changeType")
    default List<String> changeType(String changeType) {
        if (changeType == null || changeType.isEmpty()) {
            return Collections.emptyList();
        }
        return JSON.parseArray(changeType, String.class);
    }

    @Named("changeType")
    default String changeType(List<String> changeType) {
        if (changeType == null || changeType.isEmpty()) {
            return null;
        }
        return JSON.toJSONString(changeType);
    }
}
