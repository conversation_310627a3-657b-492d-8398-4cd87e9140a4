package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2OrderMapper;
import com.datatech.slgzt.dao.model.order.External2OrderDO;
import com.datatech.slgzt.model.query.External2OrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class External2OrderDAO {

    @Resource
    private External2OrderMapper mapper;

    public List<External2OrderDO> list(External2OrderQuery query) {
        return mapper.selectList(
                Wrappers.<External2OrderDO>lambdaQuery()
                        .eq(ObjNullUtils.isNotNull(query.getId()), External2OrderDO::getId, query.getId())
                        .eq(ObjNullUtils.isNotNull(query.getOrderCode()), External2OrderDO::getOrderCode, query.getOrderCode())
                        .eq(ObjNullUtils.isNotNull(query.getCreateBy()), External2OrderDO::getCreateBy, query.getCreateBy())
                        .in(ObjNullUtils.isNotNull(query.getIds()), External2OrderDO::getId, query.getIds())
                        .in(ObjNullUtils.isNotNull(query.getTenantIds()), External2OrderDO::getTenantId, query.getTenantIds())
                        .like(ObjNullUtils.isNotNull(query.getTenantName()), External2OrderDO::getTenantName, query.getTenantName())
                        .like(ObjNullUtils.isNotNull(query.getCreateByName()), External2OrderDO::getCreateByName, query.getCreateByName())
                        .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), External2OrderDO::getCreateTime, query.getCreateTimeStart())
                        .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), External2OrderDO::getCreateTime, query.getCreateTimeEnd())
                        .orderByDesc(External2OrderDO::getCreateTime)
        );
    }

    public void insert(External2OrderDO external2OrderDO) {
        mapper.insert(external2OrderDO);
    }

    public void updateById(External2OrderDO external2OrderDO) {
        mapper.updateById(external2OrderDO);
    }

    public void delete(String id) {
        mapper.deleteById(id);
    }

    public External2OrderDO getById(String id) {
        return mapper.selectById(id);
    }

    public External2OrderDO getByOrderCode(String orderCode) {
        return mapper.selectOne(Wrappers.<External2OrderDO>lambdaQuery()
                .eq(External2OrderDO::getOrderCode, orderCode));
    }
}