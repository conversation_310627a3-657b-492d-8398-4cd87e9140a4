package com.datatech.slgzt.impl.service.external2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.layout.ResRecoveryReqModel;
import com.datatech.slgzt.model.recovery.RecoveryEipModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2RecoveryResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: EIP回收
 * @author: LJ
 * @create: 2025-04-08
 **/
@Service
@Slf4j
public class External2RecoveryEipResourceServiceImpl implements External2RecoveryResourceService {

    @Resource
    private PlatformService platformService;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private BusinessService businessService;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";


    @Override
    public void recoveryResource(External2RecoveryOrderDTO dto, List<External2RecoveryOrderProductDTO> productDTOs) {
        for (External2RecoveryOrderProductDTO product : productDTOs) {
            ResRecoveryReqModel resRecoveryReqModel = new ResRecoveryReqModel();
            //参数封装
            List<ResRecoveryReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            if (ProductTypeEnum.EIP.getCode().equals(product.getProductType())) {
                RecoveryEipModel recoveryEipModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoveryEipModel.class);
                //基础参数封装
                baseParamInit(recoveryEipModel, resRecoveryReqModel, product, dto);
                ResRecoveryReqModel.ProductOrder eipProductOrder = new ResRecoveryReqModel.ProductOrder();
                eipProductOrder.setProductOrderId(recoveryEipModel.getProductOrderId().toString());
                eipProductOrder.setProductOrderType("EIP_DELETE");
                eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
                eipProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                // eipProductOrder.setGId(recoveryEipModel.getEipId());
                ResRecoveryReqModel.Attrs eipAttrs = new ResRecoveryReqModel.Attrs();
                // eipAttrs.setType(ProductTypeEnum.SLB.getCode().toUpperCase());
                eipAttrs.setResourceId(recoveryEipModel.getEipId());
                eipAttrs.setEcsResourceId(recoveryEipModel.getVmId());
                //发起回收的时候主机可能已经被回收了 或者在待回收中 如果在回收中就报错，如果已经回收了就不传入VMId
                ResourceDetailDTO detailDTO = resourceDetailManager.getByDeviceId(recoveryEipModel.getVmId());
                //如果detailDTO不是空的并且状态是回收中的话就报错
                Precondition.checkArgument(!(Objects.nonNull(detailDTO) && RecoveryStatusEnum.RECOVERING.getType().equals(detailDTO.getStatus())), "对应的主机正在回收中，请稍后再试");
                //如果主机已经是空了就不传入VMId
                if (Objects.isNull(detailDTO)) {
                    eipAttrs.setVmId(null);
                } else {
                    eipAttrs.setVmId(recoveryEipModel.getVmId());
                }
                eipProductOrder.setAttrs(eipAttrs);
                reqProductList.add(eipProductOrder);
            }
            //调用任务中心回收资源
            resRecoveryReqModel.setProductOrders(reqProductList);
            log.info("对公资源回收，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resRecoveryReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "对公资源回收失败，callLayoutOrder--编排中心初始化返回结果失败",
                    msg -> log.warn("{}, error message:{}", msg, dataMapper.getString("message")));
            log.info("对公资源回收，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
        }
        //把对应的产品都改成回收中状态
        List<Long> ids = productDTOs.stream().map(External2RecoveryOrderProductDTO::getId).collect(Collectors.toList());
        productManager.updateStatusByIds(ids, RecoveryStatusEnum.RECOVERING.getType());
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.EIP;
    }

    private void baseParamInit(RecoveryEipModel recoveryEipModel,
                               ResRecoveryReqModel resRecoveryReqModel,
                               External2RecoveryOrderProductDTO product,
                               External2RecoveryOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(recoveryEipModel.getBillId(), recoveryEipModel.getRegionCode());
        //设置计费号
        resRecoveryReqModel.setAccount(recoveryEipModel.getBillId());
        //设置业务code;
        resRecoveryReqModel.setSourceExtType(OrderTypeEnum.RECOVERY.getCode());
        //设置业务code
        resRecoveryReqModel.setBusinessCode("ECS_COMBINATION_UNSUBSCRIBE");
        //设置业务系统code
        recoveryEipModel.getAzCode();
        // 根据业务系统id获取业务系统
        CmpAppDTO cmpAppDTO = businessService.getById(recoveryEipModel.getBusinessSystemId());
        resRecoveryReqModel.setBusinessSystemCode(cmpAppDTO.getSystemCode());
        //设置客户id
        resRecoveryReqModel.setCustomId(recoveryEipModel.getCustomNo());
        //设置区域编码
        resRecoveryReqModel.setRegionCode(recoveryEipModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resRecoveryReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resRecoveryReqModel.setTenantId(tenantId);
        //设置userId
        resRecoveryReqModel.setUserId(dto.getCreatorId());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resRecoveryReqModel.setTaskSource(8);
    }
}
