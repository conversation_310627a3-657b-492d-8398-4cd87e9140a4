package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外部2回收工单DTO
 */
@Data
public class External2RecoveryOrderDTO {

    private String id;
    // 订单编号
    private String orderCode;

    //租户id
    private Long tenantId;

    //租户名称
    private String tenantName;

    //创建人
    private String creator;

    //创建时间
    private LocalDateTime createTime;

    //创建人id
    private Long creatorId;

    // jobExecutionId
    private Long jobExecutionId;

    /**
     * 回收类型。0-默认，1-用户注销
     */
    private Integer recoveryType;

    /**
     * 用户注销时使用
     */
    private String billId;
}
