package com.datatech.slgzt.impl.service.external2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.model.change.ChangeEvsModel;
import com.datatech.slgzt.model.change.ChangeMysqlModel;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.layout.ResChangeReqModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2ChangeResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年04月08日 14:10:52
 */
@Slf4j
@Service
public class External2ChangeMysqlResourceServiceImpl implements External2ChangeResourceService {
    @Resource
    private PlatformService platformService;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    @Resource
    private External2ChangeOrderProductManager changeWorkOrderProductManager;

    // todo: 历史数据的临时解决方，确保问题修正后可删除
    //  任务编排中心ResourceDetailMapper.xml-selectResourceDetailOfXxx中没有拼接RESOURCE_POOL_CODE
    //  而向任务编排中心提交任务时，该参数必须
    @Resource
    private RegionManager regionManager;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void changeResource(External2ChangeOrderDTO dto, List<External2ChangeOrderProductDTO> changeWorkOrderProducts) {
        for (External2ChangeOrderProductDTO product : changeWorkOrderProducts) {
            ResChangeReqModel resChangeReqModel = new ResChangeReqModel();
            //参数封装
            List<ResChangeReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            if (ProductTypeEnum.RDS_MYSQL.getCode().equals(product.getProductType())) {
                ChangeMysqlModel changeMysqlModel = JSONObject.parseObject(product.getPropertySnapshot(), ChangeMysqlModel.class);
                //基础参数封装
                baseParamInit(changeMysqlModel, resChangeReqModel, product, dto);
                //规格参数填充
                ResChangeReqModel.ProductOrder flavorProductOrder = new ResChangeReqModel.ProductOrder();
                ResChangeReqModel.Attrs flavorAttrs = new ResChangeReqModel.Attrs();
                flavorProductOrder.setProductOrderId(changeMysqlModel.getProductOrderId().toString());
                flavorProductOrder.setProductOrderType("RDS_MODIFY_FLAVOR");
                flavorProductOrder.setProductType(ProductTypeEnum.RDS_MYSQL.getCode());
                flavorProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                flavorAttrs.setGId(changeMysqlModel.getDeviceId());
                if (product.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    flavorAttrs.setFlavorCode(changeMysqlModel.getChangeFlavorId());
                    flavorProductOrder.setAttrs(flavorAttrs);
                }
                //存储参数填充
                if (product.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    ChangeEvsModel changeEvsModel = changeMysqlModel.getEvsModelList().get(0);
                    flavorAttrs.setStorageSize(changeEvsModel.getChangeVolumeSize());
                    flavorProductOrder.setAttrs(flavorAttrs);
                }
                reqProductList.add(flavorProductOrder);
            }
            //调用任务中心回收资源
            resChangeReqModel.setProductOrders(reqProductList);
            log.info("资源变更，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
            String bodyPara = JSON.toJSONString(resChangeReqModel);
            log.debug(bodyPara);
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(bodyPara)
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源变更失败，callLayoutOrder--编排中心初始化返回结果失败",
                    msg -> log.warn("{}, error message:{}", msg, dataMapper.getString("message")));
            log.info("资源变更，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
        }
        //把对应的产品都改成回收中状态
        List<Long> ids = changeWorkOrderProducts.stream().map(External2ChangeOrderProductDTO::getId).collect(Collectors.toList());
        changeWorkOrderProductManager.updateStatusByIds(ids, ChangeTypeProductStatusEnum.CHANGING.getCode());
    }


    private void baseParamInit(ChangeMysqlModel changeMysqlModel,
                               ResChangeReqModel resChangeReqModel,
                               External2ChangeOrderProductDTO product, External2ChangeOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(changeMysqlModel.getBillId(), changeMysqlModel.getRegionCode());
        //设置计费号
        resChangeReqModel.setAccount(changeMysqlModel.getBillId());
        //设置业务code;
        resChangeReqModel.setSourceExtType(OrderTypeEnum.EXTERNAL2_CHANGE.getCode());
        //设置业务code
        resChangeReqModel.setBusinessCode("RDS_MODIFY_FLAVOR");
        //设置客户id
        resChangeReqModel.setCustomId(changeMysqlModel.getCustomNo());

        //设置区域编码
        // todo: 历史数据的临时解决方，确保问题修正后可删除
        String regionCode = changeMysqlModel.getRegionCode();
        if (regionCode == null) {
            if (changeMysqlModel.getRegionId() != null) {
                RegionDTO regionDTO = regionManager.getById(Long.parseLong(changeMysqlModel.getRegionId()));
                regionCode = regionDTO.getCode();
            } else {
                log.warn("region id is null, RDS:{}", changeMysqlModel);
            }
        }
        resChangeReqModel.setRegionCode(regionCode);

        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resChangeReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resChangeReqModel.setTenantId(tenantId);
        //设置userId
        resChangeReqModel.setUserId(dto.getCreatorId());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resChangeReqModel.setTaskSource(8);
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.RDS_MYSQL;
    }
}
