package com.datatech.slgzt.model.req.external2.change;

import com.datatech.slgzt.model.change.ChangeReqModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年 03月31日 14:49:11
 */
@Data
public class External2ChangeOrderCreateReq {

    //--------更变设备部分字段--------------------------
    private List<ChangeReqModel> ecsPropertyList;

    private List<ChangeReqModel> evsPropertyList;

    private List<ChangeReqModel> shareEvsPropertyList;

    private List<ChangeReqModel> gcsPropertyList;

    private List<ChangeReqModel> mysqlPropertyList;

    private List<ChangeReqModel> redisPropertyList;

    private List<ChangeReqModel> obsPropertyList;

    private List<ChangeReqModel> natPropertyList;

    private List<ChangeReqModel> slbPropertyList;

    private List<ChangeReqModel> eipPropertyList;

    private List<ChangeReqModel> rdsMysqlPropertyList;

    //--------变更时间部分字段--------------------------

    private List<ChangeReqModel> ecsTimeList;

    private List<ChangeReqModel> evsTimeList;

    private List<ChangeReqModel> mysqlTimeList;

    private List<ChangeReqModel> redisTimeList;

    private List<ChangeReqModel> gcsTimeList;

    private List<ChangeReqModel> obsTimeList;

    private List<ChangeReqModel> natTimeList;

    private List<ChangeReqModel> slbTimeList;

    private List<ChangeReqModel> eipTimeList;

}
