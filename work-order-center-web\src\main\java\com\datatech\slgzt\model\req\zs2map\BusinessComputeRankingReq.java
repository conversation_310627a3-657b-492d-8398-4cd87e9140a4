package com.datatech.slgzt.model.req.zs2map;

import lombok.Data;

import java.util.List;

/**
 * 业务系统算力排名查询请求
 */
@Data
public class BusinessComputeRankingReq {
    
    /**
     * 区域编码（可选）
     */
    private String areaCode;
    
    /**
     * 卡类型/显卡型号（可选）
     */
    private String modelName;

    /**
     * 区域编码（多选）
     */
    private List<String> areaCodes;

    /**
     * 卡类型/显卡型号（多选）
     */
    private List<String> modelNames;
}
