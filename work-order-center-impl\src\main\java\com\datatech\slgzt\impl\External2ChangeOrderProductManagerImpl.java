package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.External2ChangeOrderProductManagerConvert;
import com.datatech.slgzt.dao.External2ChangeOrderProductDAO;
import com.datatech.slgzt.dao.model.order.External2ChangeOrderProductDO;
import com.datatech.slgzt.manager.External2ChangeOrderProductManager;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.query.External2ChangeOrderProductQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部2变更工单产品Manager实现类
 */
@Service
public class External2ChangeOrderProductManagerImpl implements External2ChangeOrderProductManager {

    @Autowired
    private External2ChangeOrderProductDAO external2ChangeOrderProductDAO;

    @Autowired
    private External2ChangeOrderProductManagerConvert external2ChangeOrderProductManagerConvert;

    @Override
    public void add(External2ChangeOrderProductDTO dto) {
        External2ChangeOrderProductDO dgChangeOrderProductDO = external2ChangeOrderProductManagerConvert.dto2do(dto);
        external2ChangeOrderProductDAO.insert(dgChangeOrderProductDO);
    }

    @Override
    public void update(External2ChangeOrderProductDTO dto) {
        External2ChangeOrderProductDO dgChangeOrderProductDO = external2ChangeOrderProductManagerConvert.dto2do(dto);
        external2ChangeOrderProductDAO.update(dgChangeOrderProductDO);
    }

    @Override
    public void delete(Long id) {
        external2ChangeOrderProductDAO.delete(id);
    }

    @Override
    public External2ChangeOrderProductDTO getById(Long id) {
        External2ChangeOrderProductDO dgChangeOrderProductDO = external2ChangeOrderProductDAO.getById(id);
        return external2ChangeOrderProductManagerConvert.do2dto(dgChangeOrderProductDO);
    }

    @Override
    public PageResult<External2ChangeOrderProductDTO> page(External2ChangeOrderProductQuery query) {
        // 1. start page
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        // 2. execute query
        List<External2ChangeOrderProductDO> list = external2ChangeOrderProductDAO.list(query);

        // 3. convert and return result
        return PageWarppers.box(new PageInfo<>(list), external2ChangeOrderProductManagerConvert::do2dto);
    }

    @Override
    public List<External2ChangeOrderProductDTO> list(External2ChangeOrderProductQuery query) {
        List<External2ChangeOrderProductDO> list = external2ChangeOrderProductDAO.list(query);
        return list.stream()
                .map(external2ChangeOrderProductManagerConvert::do2dto)
                .collect(Collectors.toList());
    }

    @Override
    public void updateStatusByIds(List<Long> ids, String status) {
        external2ChangeOrderProductDAO.updateStatusByIds(ids, status);
    }

    @Override
    public External2ChangeOrderProductDTO getBySubOrderId(Long subOrderId) {
        External2ChangeOrderProductDO dgChangeOrderProductDO = external2ChangeOrderProductDAO.getBySubOrderId(subOrderId);
        return external2ChangeOrderProductManagerConvert.do2dto(dgChangeOrderProductDO);
    }
}