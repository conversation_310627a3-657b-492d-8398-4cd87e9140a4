package com.datatech.slgzt.impl.yunshu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.consumer.SmsSendConsumer;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.enums.BusinessExceptionEnum;
import com.datatech.slgzt.enums.DisDimensionStatusEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.recovery.RecoveryEcsModel;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.yunshu.YunshuReportService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 入网交维云枢
 */
@Service
@Slf4j
public class YunshuReportServiceImpl implements YunshuReportService {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private NetworkOrderMapper networkOrderMapper;

    @Resource
    private NetworkSubnetOrderMapper networkSubnetOrderMapper;

    @Resource
    private OrderDataProvideService orderDataProvideService;
    @Resource
    private RecoveryWorkOrderProductManager productManager;

    @Resource
    private UserService userService;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private SmsSendConsumer smsSendConsumer;

    @Value("${yunshu.report.login.url}")
    private String loginUrl;

    @Value("${yunshu.report.login.username}")
    private String username;

    @Value("${yunshu.report.login.password}")
    private String password;

    @Value("${yunshu.report.upper}")
    private String upperUrl;

    @Value("${yunshu.report.issue}")
    private String issueUrl;

    @Value("${yunshu.report.disdimension}")
    private String disDimensionUrl;

    @Override
    public void upper(String orderId) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setTypeList(Lists.newArrayList(ProductTypeEnum.ECS.getCode(),
                ProductTypeEnum.MYSQL.getCode(),
                ProductTypeEnum.POSTGRESQL.getCode(),
                ProductTypeEnum.REDIS.getCode()));
        query.setOrderId(orderId);
        List<ResourceDetailDTO> orderProductVos = resourceDetailManager.list(query);

        orderProductVos = orderProductVos.stream().filter(productVo -> StringUtils.isNotBlank(productVo.getConfigId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderProductVos)) {
            return;
        }
        Map<String, ResourceDetailDTO> productMap = orderProductVos.stream().collect(Collectors.toMap(ResourceDetailDTO::getDeviceId, product -> product));
        List<String> deviceIds = orderProductVos.stream().map(ResourceDetailDTO::getDeviceId).collect(Collectors.toList());
        List<VirtualMachineDTO> virtualMachineList = vmMapper.getByIdList(deviceIds);


        List<Map<String, String>> req = new ArrayList<>();
        for (VirtualMachineDTO virtualMachine : virtualMachineList) {
            ResourceDetailDTO orderProductVo = productMap.get(virtualMachine.getId());
            //封装上报参数
            Map<String, String> map = new HashMap<>();
            map.put("cloud", virtualMachine.getCloud());
            map.put("instanceId", orderProductVo.getConfigId());
            String status = "关机";
            if ("RUNING".equals(virtualMachine.getStatus())) {
                status = "开机";
            } else if ("SHUTOFF".equals(virtualMachine.getStatus())) {
                status = "关机";
            }
            map.put("deviceStatus", status);
            map.put("name", orderProductVo.getDeviceName());
            map.put("version", virtualMachine.getImagesName());
            map.put("vcpu", virtualMachine.getVcpus().toString());
            map.put("mem", String.valueOf(virtualMachine.getRam() / 1024));
            map.put("uuid", virtualMachine.getInstanceUuid());
            List<Volume> volumeList = vmMapper.getVolume(virtualMachine.getId());
            long vDisks = volumeList.stream().filter(volume -> volume.getVolumeSize() != null).mapToLong(Volume::getVolumeSize).sum();
            map.put("vDisks", String.valueOf(vDisks));
            String ipAddress = orderProductVo.getManageIp();
            List<IpAddress> ipList = vmMapper.getIpAddress(virtualMachine.getId(), "ECS");
            if (CollectionUtils.isNotEmpty(ipList) && StringUtils.isNotEmpty(ipAddress)) {
                Map<String, String> ipAddressMap = ipList.stream().collect(Collectors.toMap(IpAddress::getType, IpAddress::getIp, (oldValue, newValue) -> oldValue));
                if (ipAddress.length() <= 18) {
                    map.put("ipv4", ipAddress);
                } else {
                    map.put("ipv6", ipAddress);
                }
                String ips = String.join(",", ipAddressMap.values());
                map.put("otherIps", ips);
            }
            req.add(map);
        }
        String json = JSONObject.toJSONString(req);
        log.info("upper yunshu 请求参数：{}", json);
        String resultString = doPostJson(upperUrl, json, getHeader());
        log.info("upper yunshu 返回参数：{}", resultString);
        JSONObject resultJson = JSONObject.parseObject(resultString);
        if (!resultJson.getBoolean("success")) {
            log.error("入网交维失败：upper yunshu error : {}", resultJson.toJSONString());
            throw new BusinessException(BusinessExceptionEnum.YUNSHU_UPPER_FAILED.code, BusinessExceptionEnum.YUNSHU_UPPER_FAILED.message);
        }
    }

    @Override
    public void issue(String networkId, Integer type) {
        NetworkOrder networkOrder = networkOrderMapper.selectById(networkId);
        //融合边缘云也要走这个逻辑
        if ((!CatalogueDomain.PLATFORM_CHILD.getCode().equals(networkOrder.getDomainCode())
                && !CatalogueDomain.VMWARE.getCode().equals(networkOrder.getDomainCode()))
                || (StringUtils.isBlank(networkOrder.getVlan()) && StringUtils.isBlank(networkOrder.getVlanId()))) {
            return;
        }
        List<NetworkSubnetOrder> list = networkSubnetOrderMapper.selectByNetworkId(networkOrder.getId());
        UserCenterUserDTO userDTO = userService.getUserById(networkOrder.getCreatedBy());
        NetworkSubnetOrder networkSubnetOrder = list.get(0);
        RegionCmdbDTO regionCmdb = vmMapper.getRegionCmdb(networkOrder.getRegionCode());
        Map<String, Object> map = new HashMap<>();
        map.put("cloudName", regionCmdb.getCloud());
        map.put("orderId", networkOrder.getId());
        map.put("type", type);
        map.put("bussness", networkOrder.getBusinessSysName());
        map.put("relatedPool", regionCmdb.getCmdbName());
        map.put("gateway", networkSubnetOrder.getGateway());
        map.put("vlan", networkOrder.getVlan());
        map.put("networkSegment", networkSubnetOrder.getCidr());
        map.put("bussnessContact", userDTO.getLoginName());
        map.put("vpc", networkOrder.getName());
        map.put("portGroup", "");

        String json = JSONObject.toJSONString(map);
        log.info("upper yunshu 请求参数：{}", json);
        String resultString = doPostJson(issueUrl, json, getHeader());
        log.info("upper yunshu 返回参数：{}", resultString);
        JSONObject resultJson = JSONObject.parseObject(resultString);
        if (!resultJson.getBoolean("success")) {
            log.error("入网交维失败：upper yunshu error : {}", resultJson.toJSONString());
            throw new BusinessException(BusinessExceptionEnum.YUNSHU_UPPER_FAILED.code, BusinessExceptionEnum.YUNSHU_UPPER_FAILED.message);
        }
    }

    private String login(String username, String password) {
        Map<String, String> map = new HashMap<>();
        map.put("username", username);
        map.put("password", password);
        String s = JSONObject.toJSONString(map);
        String resultString = doPostJson(loginUrl, s, null);
        String xToken = Optional.ofNullable(JSON.parseObject(resultString).getJSONObject("data")).flatMap(jsonResult -> Optional.ofNullable(jsonResult.getString("x_token"))).filter(StringUtils::isNotEmpty).orElseThrow(() -> new RuntimeException("登录云枢返回异常,请联系管理员" + resultString));
        return xToken;
    }

    private String doPostJson(String url, String json, Map<String, String> reqHeader) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            log.info("yunshu doPostJson url ：{}", url);

            // 创建请求内容
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            // 设置通用属性
            httpPost.setHeader("Accept", "*/*");
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Connection", "Keep-Alive");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)");
            if (!ObjectUtils.isEmpty(reqHeader)) {
                for (String key : reqHeader.keySet()) {
                    httpPost.setHeader(key, reqHeader.get(key));
                }
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            } else {
                log.error("入网交维失败：yunshu doPostJson post请求返回异常，url = {}, rep = {}", url, response);
                throw new BusinessException(BusinessExceptionEnum.YUNSHU_UPPER_FAILED.code, BusinessExceptionEnum.YUNSHU_UPPER_FAILED.message);
            }
        } catch (Exception e) {
            log.error("入网交维失败：yunshu doPostJson error {}", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("入网交维失败：yunshu doPostJson error {}", e);
            }
        }
        return resultString;
    }

    /**
     * 退维
     *
     * @param recoveryWorkOrderProducts
     */
    @Override
    public void disdimension(List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProducts, RecoveryWorkOrderDTO recoveryWorkOrderDTO) {
        List<Map<String, String>> req = new ArrayList<>();
        List<String> list = new ArrayList<>();
        // 不需要退维数据
        List<Long> noDisdimensionList = new ArrayList<>();
        String orderCode = recoveryWorkOrderDTO.getOrderCode();
        for (RecoveryWorkOrderProductDTO product : recoveryWorkOrderProducts) {
            if (!ProductTypeEnum.ECS.getCode().equals(product.getProductType())
                    && !ProductTypeEnum.MYSQL.getCode().equals(product.getProductType())
                    && !ProductTypeEnum.POSTGRESQL.getCode().equals(product.getProductType())
                    && !ProductTypeEnum.REDIS.getCode().equals(product.getProductType())) {
                continue;
            }
            RecoveryEcsModel recoveryEcsModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoveryEcsModel.class);
            if (StringUtils.isBlank(product.getCmdbId())) {
                noDisdimensionList.add(product.getId());
                log.warn("该资源暂未同步到CMDB，无需删除 :{}", product.getPropertySnapshot());
                continue;
            }
            Map<String, String> map = new HashMap<>();
            list.add(product.getCmdbId());
            map.put("orderId", product.getWorkOrderId());
            map.put("instanceId", product.getCmdbId());
            map.put("name", recoveryEcsModel.getVmName());
            String ipAddress = recoveryEcsModel.getManageIp();
            if (StringUtils.isNotEmpty(ipAddress)) {
                if (ipAddress.length() <= 18) {
                    map.put("ipv4", ipAddress);
                }
            }
            req.add(map);
        }
        if (CollectionUtils.isNotEmpty(req)) {
            String json = JSONObject.toJSONString(req);
            log.info("disdimension yunshu 请求参数：{}", json);
            String resultString = this.doPostJson(disDimensionUrl + "/" + orderCode, json, getHeader());
            log.info("disdimension yunshu 返回参数：{}", resultString);
            JSONObject resultJson = JSONObject.parseObject(resultString);
            if (!resultJson.getBoolean("success")) {
                log.error("发起退维失败：disdimension yunshu error : {}", resultJson.toJSONString());
                //修改状态为退维失败
                productManager.updateHcmByCmdbIds(list, DisDimensionStatusEnum.FAIL.getCode());
                throw new BusinessException(BusinessExceptionEnum.YUNSHU_UPPER_FAILED.code, BusinessExceptionEnum.YUNSHU_UPPER_FAILED.message);
            }
            //修改状态为退维成功
            productManager.updateHcmByCmdbIds(list, DisDimensionStatusEnum.RUNNING.getCode());
        }
        if (CollectionUtils.isNotEmpty(noDisdimensionList)) {
            productManager.updateHcmByIds(noDisdimensionList, DisDimensionStatusEnum.SUCCESS.getCode());
        }


    }

    public void callback(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(BusinessExceptionEnum.INSTANCE_ID_NULL_ERROR.code, BusinessExceptionEnum.INSTANCE_ID_NULL_ERROR.message);
        }
        productManager.updateHcmByCmdbIds(list, DisDimensionStatusEnum.SUCCESS.getCode());
        /*for (String cmdbId : list) {
            ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getConfigId(cmdbId);
            UserCenterUserDTO userCenterUserDTO = userService.getUserById(resourceDetailDTO.getApplyUserId());
            String message = "主机“" +resourceDetailDTO.getDeviceName() + "”退维成功，请及时删除跟资源相关的防火墙策略和4A信息";
            smsSendConsumer.sendMessage(userCenterUserDTO.getPhone(),message);
        }*/

    }

    private Map<String, String> getHeader() {
        String xToken = this.login(username, password);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-token", xToken);
        headers.put("x-local", "true");
        return headers;
    }
}
