package com.datatech.slgzt.model.vo.obs;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description: 存储桶性能数据
 * @author: LK
 * @create: 2025-09-18 17:33
 **/
@Data
public class BucketVO {

    private String bucketName;

    /**
     * 配额容量
     */
    private String quota;

    /**
     * 已使用配额容量
     */
    private String useQuota;

    /**
     * 使用率
     */
    private String useQuotaPercent;

    /**
     * 上传ops
     */
    private List<DataPoint> opsUpload;

    /**
     * 下载ops
     */
    private List<DataPoint> opsDownload;

    /**
     * 上传带宽
     */
    private List<DataPoint> bandwidthUpload;

    /**
     * 下载带宽
     */
    private List<DataPoint> bandwidthDownload;

    @Data
    public static class DataPoint {
        private String point;
        private Double data;
    }
}
