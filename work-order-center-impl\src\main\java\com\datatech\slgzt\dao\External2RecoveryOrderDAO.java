package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2RecoveryOrderMapper;
import com.datatech.slgzt.dao.model.order.External2RecoveryOrderDO;
import com.datatech.slgzt.model.query.External2RecoveryOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2回收工单DAO
 */
@Repository
public class External2RecoveryOrderDAO {
    @Resource
    private External2RecoveryOrderMapper mapper;


    public External2RecoveryOrderDO getById(String id) {
        return mapper.selectById(id);
    }


    public String insert(External2RecoveryOrderDO recoveryWorkOrderDO) {
        mapper.insert(recoveryWorkOrderDO);
        return recoveryWorkOrderDO.getId();
    }


    public void update(External2RecoveryOrderDO orderDO) {
        mapper.updateById(orderDO);
    }

    public List<External2RecoveryOrderDO> list(External2RecoveryOrderQuery query) {
        LambdaQueryWrapper<External2RecoveryOrderDO> queryWrapper = Wrappers.<External2RecoveryOrderDO>lambdaQuery()
                .isNull(Boolean.TRUE.equals(query.getJobExecutionIdNull()), External2RecoveryOrderDO::getJobExecutionId);
        // 回收类型。null-全部,0-默认，1-用户注销
        queryWrapper.eq(ObjNullUtils.isNotNull(query.getRecoveryType()), External2RecoveryOrderDO::getRecoveryType, query.getRecoveryType());
        queryWrapper.like(ObjNullUtils.isNotNull(query.getOrderCode()), External2RecoveryOrderDO::getOrderCode, query.getOrderCode())
                .like(ObjNullUtils.isNotNull(query.getCreator()), External2RecoveryOrderDO::getCreator, query.getCreator())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), External2RecoveryOrderDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), External2RecoveryOrderDO::getCreateTime, query.getCreateTimeEnd())
                .in(ObjNullUtils.isNotNull(query.getTenantIds()), External2RecoveryOrderDO::getTenantId, query.getTenantIds());
        queryWrapper.orderByDesc(External2RecoveryOrderDO::getCreateTime);
        return mapper.selectList(queryWrapper);
    }
}
