package com.datatech.slgzt.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;

import java.util.List;

/**
 * 外部2资源回收总接口
 */
public interface External2RecoveryResourceService {

    void recoveryResource(External2RecoveryOrderDTO dto, List<External2RecoveryOrderProductDTO> productDTOs);

    /**
     * 注册
     */
    ProductTypeEnum registerOpenService();
}
