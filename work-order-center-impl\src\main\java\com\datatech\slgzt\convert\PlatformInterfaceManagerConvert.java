package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.PlatformInterfaceDO;
import com.datatech.slgzt.model.dto.PlatformInterfaceDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface PlatformInterfaceManagerConvert {

    PlatformInterfaceDTO do2dto(PlatformInterfaceDO platformInterfaceDO);

    PlatformInterfaceDO dto2do(PlatformInterfaceDTO dto);
}
