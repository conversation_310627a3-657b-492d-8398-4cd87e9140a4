package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("woc_gpu_device_metrics_distributed")
public class  DeviceCardMetricsDO {


    @TableId(value = "ID", type = IdType.ID_WORKER)
    private Long id;

    /**
     * GPU/NPU卡序列号/uuid
     */
    @TableField("DEVICE_ID")
    private String deviceId;

    /**
     * 算力利用率
     */
    @TableField("GPU_UTIL_PERCENT")
    private Double gpuUtilPercent;

    /**
     * 显存利用率
     */
    @TableField("MEM_UTIL_PERCENT")
    private Double memUtilpercent;

    /**
     * 显存大小 显存大小（GB）
     */
    @TableField("MEMORY_USAGE")
    private Integer memoryUsage;

    /**
     * 算力能耗top
     */
    @TableField("DEV_POWER_USAGE")
    private Double devPowerUsage;

    /**
     * gpu温度
     */
    @TableField("DEV_GPU_TEMP")
    private Double devGpuTemp;

    /**
     * 区域编码
     */
    @TableField("AREA_CODE")
    private String areaCode;


    /**
     * 时间单位状态
     */
    @TableField("GPU_TIME")
    private String gpuTime;


    @TableField("DEVICE_TYPE")
    private String deviceType;

    @TableField("CREATED_AT")
    private LocalDateTime createdAt;



    /**
     * 设备型号
     */
    @TableField("MODEL_NAME")
    private String modelName;
    /**
     * 指标来源 虚机/物理机
     */
    @TableField("METRIC_SOURCE")
    private String  metricSource;

    /**
     * 任务数
     */
    @TableField("ALLOCATION_COUNT")
    private String  allocationCount;

    /**
     * 业务系统名称（用于关联查询）
     */
    @TableField(exist = false)
    private String businessSystemName;

    /**
     * 部门名称（用于关联查询）
     */
    @TableField(exist = false)
    private String deptName;

}
