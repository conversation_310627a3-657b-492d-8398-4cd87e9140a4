package com.datatech.slgzt.impl.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.External2RecoveryOrderManager;
import com.datatech.slgzt.manager.External2RecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import com.datatech.slgzt.service.external2.External2RecoveryResourceService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Slf4j
@Configuration
public class External2RecoveryJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<External2RecoveryResourceService> recoveryResourceServices;

    private final Map<String, External2RecoveryResourceService> serviceMap = Maps.newHashMap();

    @Resource
    private External2RecoveryJobListener external2RecoveryJobListener;

    @Resource
    private External2RecoveryOrderProductManager productManager;

    @Resource
    private External2RecoveryOrderManager orderManager;

    @Value("${http.userCenterUrl}")
    private String userCenterUrl;

    // 2.10外部租户注销之后回调的接口(新增0716)
    String deleteAccountUrl = "/ccmp/usercenter/tenant/cancel/";


    @Bean("external2ProductRecoveryJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("external2ProductRecoveryJob").incrementer(new RunIdIncrementer())
                .listener(external2RecoveryJobListener)
                .start(external2ProductRecoveryInit())
                .next(external2EcsRecovery())
                .next(external2GcsRecovery())
                .next(external2MysqlRecovery())
                .next(external2SlbRecovery())
                .next(external2NatRecovery())
                .next(external2EipRecovery())
                .next(external2EvsRecovery())
                .next(external2ShareEvsRecovery())
                .next(external2ObsRecovery())
                .next(external2VpnRecovery())
                .next(external2BackupRecovery())
                .next(external2VpcRecovery())
                .next(external2DeleteAccount())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("external2ProductRecoveryInit")
    public Step external2ProductRecoveryInit() {
        return stepBuilderFactory.get("external2ProductRecoveryInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("external2DeleteAccount")
    public Step external2DeleteAccount() {
        return stepBuilderFactory.get("external2DeleteAccount").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 根据orderId，获取external2RecoveryOrder
            External2RecoveryOrderDTO orderDTO = orderManager.getById(orderId);
            if (orderDTO.getRecoveryType() != null && orderDTO.getRecoveryType().equals(1)) {
                deleteAccount(orderDTO.getBillId());
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean("external2EcsRecovery")
    public Step external2EcsRecovery() {
        return stepBuilderFactory.get("external2EcsRecovery").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    @Bean("external2GcsRecovery")
    public Step external2GcsRecovery() {
        return stepBuilderFactory.get("external2GcsRecovery").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    @Bean("external2MysqlRecovery")
    public Step external2MysqlRecovery() {
        return stepBuilderFactory.get("external2MysqlRecovery").tasklet(getProductTasklet(ProductTypeEnum.RDS_MYSQL)).build();
    }

    @Bean("external2SlbRecovery")
    public Step external2SlbRecovery() {
        return stepBuilderFactory.get("external2SlbRecovery").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }


    @Bean("external2NatRecovery")
    public Step external2NatRecovery() {
        return stepBuilderFactory.get("external2NatRecovery").tasklet(getProductTasklet(ProductTypeEnum.NAT)).build();
    }

    @Bean("external2EipRecovery")
    public Step external2EipRecovery() {
        return stepBuilderFactory.get("external2EipRecovery").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    @Bean("external2EvsRecovery")
    public Step external2EvsRecovery() {
        return stepBuilderFactory.get("external2EvsRecovery").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    @Bean("external2ShareEvsRecovery")
    public Step external2ShareEvsRecovery() {
        return stepBuilderFactory.get("external2ShareEvsRecovery").tasklet(getProductTasklet(ProductTypeEnum.SHARE_EVS)).build();
    }

    @Bean("external2ObsRecovery")
    public Step external2ObsRecovery() {
        return stepBuilderFactory.get("external2ObsRecovery").tasklet(getProductTasklet(ProductTypeEnum.OBS)).build();
    }

    @Bean("external2VpnRecovery")
    public Step external2VpnRecovery() {
        return stepBuilderFactory.get("external2VpnRecovery").tasklet(getProductTasklet(ProductTypeEnum.VPN)).build();
    }

    @Bean("external2BackupRecovery")
    public Step external2BackupRecovery() {
        return stepBuilderFactory.get("external2BackupRecovery").tasklet(getProductTasklet(ProductTypeEnum.BACKUP)).build();
    }

    @Bean("external2VpcRecovery")
    public Step external2VpcRecovery() {
        return stepBuilderFactory.get("external2VpcRecovery").tasklet(getProductTasklet(ProductTypeEnum.VPC)).build();
    }

    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 根据orderId，获取external2RecoveryOrder
            External2RecoveryOrderDTO orderDTO = orderManager.getById(orderId);
            // 通过orderId获取到product对象
            List<External2RecoveryOrderProductDTO> productDTOs =
                    productManager.listByWorkOrderId(orderId).stream()
                            .filter(i -> productType.getCode().equals(i.getProductType())
                                    // 只发起主产品回收
                                    && i.getParentProductId().equals(0L)
                                    // 已取消回收，不执行
                                    && !RecoveryStatusEnum.CANCELED.getType().equals(i.getRecoveryStatus()))
                            .collect(Collectors.toList());
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<External2RecoveryOrderProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                External2RecoveryResourceService createService = serviceMap.get(productType.getCode());
                External2RecoveryOrderProductDTO productDTO = productDTOOptional.get();
                try {
                    // 开通
                    createService.recoveryResource(orderDTO, Collections.singletonList(productDTO));
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    External2RecoveryOrderProductDTO dto = new External2RecoveryOrderProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setRecoveryStatus(RecoveryStatusEnum.RECLAIM_FAILURE.getType());
                    dto.setMessage(e.getMessage());
                    productManager.update(dto);
                    // 发短信
//                    orderManager.sendFailSms(productDTO.getId());
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }


    @Override
    public void afterPropertiesSet() {
        for (External2RecoveryResourceService service : recoveryResourceServices) {
            serviceMap.put(service.registerOpenService().getCode(), service);
        }
    }

    private Optional<External2RecoveryOrderProductDTO> filterProduct(List<External2RecoveryOrderProductDTO> productDTOs) {
        return productDTOs.stream().filter(i ->
                i.getParentProductId() == 0
                        && (// 没有回收状态 或者 带回收/回收失败
                        i.getRecoveryStatus() == null ||
                                (RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().equals(i.getRecoveryStatus())
                                        || RecoveryStatusEnum.RECLAIM_FAILURE.getType().equals(i.getRecoveryStatus())))
        ).findFirst();
    }

    private void deleteAccount(String billId) {
        Long tenantId;
        String url = userCenterUrl + deleteAccountUrl + billId;
        log.info("request user center url:" + url);
        Mapper mapperData = OkHttps.sync(url)
                .addHeader("RemoteUser", "BusinessCenter")
                .delete()
                .getBody()
                .toMapper();
        Precondition.checkArgument(mapperData.getInt("success") == 1, "资源中心调用失败:" + mapperData.getString("message"));
    }
}
