package com.datatech.slgzt.impl.service.external2;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.cloudPort.IWocCloudPortManager;
import com.datatech.slgzt.convert.CloudPortManagerConvert;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.nostander.CloudPortModel;
import com.datatech.slgzt.service.external2.External2ResOpenService;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class External2ResOpenCloudPortServiceImpl implements External2ResOpenService {

    @Resource
    private DagOrderManager dagOrderManager;

    @Resource
    private CloudPortManagerConvert cloudPortManagerConvert;

    @Resource
    private IWocCloudPortManager IWocCloudPortManager;


    @Override
    public Object openResource(External2OrderProductDTO productDTO) {
        log.info("算力编排云端口参数：{}", JSON.toJSONString(productDTO));
        String productType = productDTO.getProductType();
        Precondition.checkArgument(ProductTypeEnum.CLOUDPORT.getCode().equals(productType), "算力编排资源类型不匹配");
        DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
        CloudPortModel cloudPortModel = JSON.parseObject(productDTO.getPropertySnapshot(), CloudPortModel.class);
        IWocCloudPortManager.create(cloudPortManagerConvert.model2dto(cloudPortModel));
        return null;

    }


    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.CLOUDPORT;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
