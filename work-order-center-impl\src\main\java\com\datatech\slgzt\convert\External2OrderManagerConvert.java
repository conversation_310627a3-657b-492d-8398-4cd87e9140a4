package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.External2OrderDO;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface External2OrderManagerConvert {

    External2OrderDTO do2dto(External2OrderDO external2Order);

    External2OrderDO dto2do(External2OrderDTO external2OrderDTO);

    List<External2OrderDTO> dos2DTOs(List<External2OrderDO> orderDOS);

    List<External2OrderDO> dtoList2DOs(List<External2OrderDTO> orderDTOS);
}
