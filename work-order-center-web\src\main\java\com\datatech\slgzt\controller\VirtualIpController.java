package com.datatech.slgzt.controller;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.VirtualIpWebConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.manager.VirtualIpManager;
import com.datatech.slgzt.manager.VpcOrderManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.VirtualIpDTO;
import com.datatech.slgzt.model.dto.VpcOrderExtDTO;
import com.datatech.slgzt.model.query.VirtualIpQuery;
import com.datatech.slgzt.model.req.vip.*;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.VirtualIpVO;
import com.datatech.slgzt.service.VirtualIpService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.Lists;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 虚拟ip控制器
 */
@RestController
@RequestMapping("/virtualIp")
public class VirtualIpController {

    @Resource
    private VirtualIpManager virtualIpManager;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private VirtualIpWebConvert virtualIpWebConvert;

    @PostMapping("/page")
    public CommonResult<PageResult<VirtualIpVO>> page(@RequestBody VirtualIpPageReq req) {
        VirtualIpQuery query = virtualIpWebConvert.convert(req);
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<Long> tenantIds = tenantManager.listRelTenantIdByUserId(currentUserId);
        if (ObjNullUtils.isNull(tenantIds)) {
            tenantIds= Lists.newArrayList(-1L);
        }
        query.setTenantIdList(tenantIds);
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArray(oacRoles, UserCenterRoleDTO::getCode);
        if (roles.contains("super_admin") ||roles.contains("operation_group")) {
            query.setTenantIdList(null);
        }
        PageResult<VirtualIpDTO> page = virtualIpManager.page(query);
        return CommonResult.success(PageWarppers.box(page, virtualIpWebConvert::convert));
    }

    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(description = "添加虚拟ip",operationType = "CREATE")
    public CommonResult<Void> add(@RequestBody VirtualIpAddReq req) {
        VirtualIpDTO dto = virtualIpWebConvert.convert(req);
        VpcOrderExtDTO vpcOrderExtDTO = vpcOrderManager.getById(req.getVpcId());
        Precondition.checkArgument(vpcOrderExtDTO, "vpcId不存在");
        dto.setBusinessSystemId(vpcOrderExtDTO.getBusinessSysId().toString());
        dto.setBusinessSystemName(vpcOrderExtDTO.getBusinessSysName());
        dto.setTenantId(vpcOrderExtDTO.getTenantId());
        dto.setGid(IdUtil.fastSimpleUUID());
        List<String> ipList = virtualIpManager.getIpListBySubnetId(req.getSubnetId());
        Precondition.checkArgument(!ipList.contains(req.getIpAddress()), "ip已被占用");
        //virtualIpService.createVirtualIp(dto);
        virtualIpManager.add(dto);
        return CommonResult.success(null);
    }

    @PostMapping("/update")
    @OperationLog(description = "更新虚拟ip",operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody VirtualIpUpdateReq req) {
        VirtualIpDTO dto = virtualIpWebConvert.convert(req);
        virtualIpManager.update(dto);
        return CommonResult.success();
    }

    @PostMapping("/delete")
    @OperationLog(description = "删除虚拟ip",operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody VirtualIpIdReq req) {
        //virtualIpService.deleteVirtualIp(req.getId());
        virtualIpManager.delete(req.getId());
        return CommonResult.success();
    }

    @PostMapping("/detail")
    @OperationLog(description = "获取虚拟ip详情",operationType = "READ")
    public CommonResult<VirtualIpVO> detail(@RequestBody VirtualIpIdReq req) {
        VirtualIpDTO dto = virtualIpManager.getById(req.getId());
        return CommonResult.success(virtualIpWebConvert.convert(dto));
    }

    /**
     * 获取未使用的ip列表
     *
     * @param req
     * @return
     */
    @PostMapping("/getUnusedIpList")
    public CommonResult<List<String>> getUnusedIpList(@RequestBody VirtualIpUnusedIpGetReq req) {
        List<String> ipList = virtualIpManager.getUnusedIpList(req.getSubnetId(), req.getCidr(),req.getKeyword());
        //截取100个
        return CommonResult.success(ipList.stream().limit(100).collect(Collectors.toList()));
    }


} 