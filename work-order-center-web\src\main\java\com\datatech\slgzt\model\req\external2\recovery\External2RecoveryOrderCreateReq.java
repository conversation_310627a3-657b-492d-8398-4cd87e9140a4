package com.datatech.slgzt.model.req.external2.recovery;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 16:43:13
 */
@Data
public class External2RecoveryOrderCreateReq {

    //这里的id都给资源表的goodsOrderId好了

    //回收ecsId列表
    private List<Long> ecsIdList;

    //回收gcsId列表
    private List<Long> gcsIdList;

    //mysqlId列表
    private List<Long> rdsMysqlIdList;

    //回收redisId列表
    private List<Long> redisIdList;

    //回收evs列表
    private List<Long> evsIdList;
    //回收共享evs列表
    private List<Long> sharedEvsIdList;

    //回收eip列表
    private List<Long> eipIdList;

    //回收nat列表
    private List<Long> natIdList;

    //回收obs列表
    private List<Long> obsIdList;

    //回收slb列表
    private List<Long> slbIdList;

    //vpc网络回收id列表
    private List<String> vpcIdList;

    //网络回收id列表
    private List<String> networkIdList;

    //回收云备份列表
    private List<Long> backupIdList;

    //回收vpn列表
    private List<Long> vpnIdList;

    //回收nas列表
    private List<Long> nasIdList;

    //回收裸金属列表
    private List<Long> pmIdList;

    //同步回收列表
    private List<Long> syncRecoveryIdList;

}
