package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.External2RecoveryOrderProductDO;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface External2RecoveryOrderProductManagerConvert {

    External2RecoveryOrderProductDTO do2dto(External2RecoveryOrderProductDO external2RecoveryOrderProductDO);

    External2RecoveryOrderProductDO dto2do(External2RecoveryOrderProductDTO external2RecoveryOrderProductDTO);

    List<External2RecoveryOrderProductDTO> dos2DTOs(List<External2RecoveryOrderProductDO> productDOS);

    List<External2RecoveryOrderProductDO> dtoList2DOs(List<External2RecoveryOrderProductDTO> productDTOS);
}
