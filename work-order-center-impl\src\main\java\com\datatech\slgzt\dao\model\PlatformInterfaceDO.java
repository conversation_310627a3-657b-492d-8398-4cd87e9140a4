package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR> 2025/06/06 9:43
 */
@Data
@TableName("MC_PF_INTERFACE_INIT_T")
public class PlatformInterfaceDO {
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 平台code
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * 平台名称
     */
    @TableField("DOMAIN_NAME")
    private String domainName;

    /**
     * 资源类型
     */
    @TableField("RES_TYPE")
    private String resType;

    @TableField("RES_NAME")
    private String resName;

    /**
     * 接口地址
     */
    @TableField("URL")
    private String url;

    /**
     * 接口名称
     */
    @TableField("URL_NOTE")
    private String urlNote;

    /**
     * 是否生效1-生效，0-未生效
     */
    @TableField("STATUS")
    private String status;

    @TableField("SORT")
    private Integer sort;
}
