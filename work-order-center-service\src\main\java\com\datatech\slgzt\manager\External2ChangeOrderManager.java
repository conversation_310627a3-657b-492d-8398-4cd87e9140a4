package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 外部2变更工单Manager接口
 */
public interface External2ChangeOrderManager {

    /**
     * 新增
     */
    void add(External2ChangeOrderDTO dto);

    /**
     * 更新
     */
    void update(External2ChangeOrderDTO dto);

    /**
     * 删除
     */
    void delete(String id);

    /**
     * 根据ID查询
     */
    External2ChangeOrderDTO getById(String id);

    /**
     * 分页查询
     */
    PageResult<External2ChangeOrderDTO> page(External2ChangeOrderQuery query);

    /**
     * 列表查询
     */
    List<External2ChangeOrderDTO> list(External2ChangeOrderQuery query);
}
