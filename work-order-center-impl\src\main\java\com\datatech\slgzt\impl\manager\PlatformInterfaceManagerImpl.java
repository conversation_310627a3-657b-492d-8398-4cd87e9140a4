package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.PlatformInterfaceManagerConvert;
import com.datatech.slgzt.dao.PlatformInterfaceDAO;
import com.datatech.slgzt.manager.PlatformInterfaceManager;
import com.datatech.slgzt.model.dto.PlatformInterfaceDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 13:58:58
 */
@Service
public class PlatformInterfaceManagerImpl implements PlatformInterfaceManager {

    @Resource
    private PlatformInterfaceDAO dao;

    @Resource
    private PlatformInterfaceManagerConvert convert;

    @Override
    public void insert(PlatformInterfaceDTO dto) {
        dao.insert(convert.dto2do(dto));
    }

    @Override
    public void delete(String name) {
        dao.delete(name);
    }
}
