package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.External2OrderProductDO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface External2OrderProductManagerConvert {

    External2OrderProductDTO do2dto(External2OrderProductDO external2OrderProduct);

    External2OrderProductDO dto2do(External2OrderProductDTO external2OrderProductDTO);

    List<External2OrderProductDTO> dos2DTOs(List<External2OrderProductDO> productDOS);

    List<External2OrderProductDO> dtoList2DOs(List<External2OrderProductDTO> productDTOS);
}
