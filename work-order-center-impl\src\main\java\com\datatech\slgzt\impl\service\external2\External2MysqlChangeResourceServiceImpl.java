package com.datatech.slgzt.impl.service.external2;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.layout.ResChangeReqModel;
import com.datatech.slgzt.service.external2.External2ChangeResourceService;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外部2 MySQL变更资源服务实现类
 */
@Slf4j
@Service
public class External2MysqlChangeResourceServiceImpl implements External2ChangeResourceService {

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void changeResource(External2ChangeOrderDTO dto, List<External2ChangeOrderProductDTO> changeWorkOrderProducts) {
        log.info("外部2 MySQL变更资源开始，工单ID: {}", dto.getId());

        for (External2ChangeOrderProductDTO productDTO : changeWorkOrderProducts) {
            if (ProductTypeEnum.MYSQL.getCode().equals(productDTO.getProductType())) {
                ResChangeReqModel resChangeReqModel = new ResChangeReqModel();
                resChangeReqModel.setProductType(productDTO.getProductType());
                resChangeReqModel.setPropertySnapshot(productDTO.getPropertySnapshot());
                resChangeReqModel.setSubOrderId(productDTO.getSubOrderId());
                resChangeReqModel.setTenantId(dto.getTenantId());
                resChangeReqModel.setUserId(dto.getCreateBy());
                //设置来源固定8这个是给任务中心用的来判断回调的
                resChangeReqModel.setTaskSource(8);

                //调用任务编排中心
                String result = OkHttps.sync(layoutCenter + "/" + layoutTaskInitUrl)
                        .bodyType("json")
                        .setBodyPara(resChangeReqModel)
                        .post()
                        .getBody()
                        .toString();

                log.info("外部2 MySQL变更调用任务编排中心结果: {}", result);
            }
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.MYSQL;
    }
}
