package com.datatech.slgzt.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;

import java.util.List;

/**
 * 外部2变更资源服务接口
 */
public interface External2ChangeResourceService {

    void changeResource(External2ChangeOrderDTO dto, List<External2ChangeOrderProductDTO> changeWorkOrderProducts);

    /**
     * 注册
     */
    ProductTypeEnum registerOpenService();

}
