package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.External2OrderManagerConvert;
import com.datatech.slgzt.dao.External2OrderDAO;
import com.datatech.slgzt.dao.model.order.External2OrderDO;
import com.datatech.slgzt.manager.External2OrderManager;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.query.External2OrderQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class External2OrderManagerImpl implements External2OrderManager {

    @Resource
    private External2OrderDAO external2OrderDAO;

    @Resource
    private External2OrderManagerConvert external2OrderManagerConvert;

    @Override
    public List<External2OrderDTO> list(External2OrderQuery query) {
        List<External2OrderDO> list = external2OrderDAO.list(query);
        return external2OrderManagerConvert.dos2DTOs(list);
    }

    @Override
    public PageResult<External2OrderDTO> page(External2OrderQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<External2OrderDO> list = external2OrderDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), external2OrderManagerConvert::do2dto);
    }

    @Override
    public String insert(External2OrderDTO dto) {
        External2OrderDO external2OrderDO = external2OrderManagerConvert.dto2do(dto);
        external2OrderDAO.insert(external2OrderDO);
        return dto.getId();
    }

    @Override
    public void update(External2OrderDTO dto) {
        External2OrderDO external2OrderDO = external2OrderManagerConvert.dto2do(dto);
        external2OrderDAO.updateById(external2OrderDO);
    }

    @Override
    public void delete(String id) {
        external2OrderDAO.delete(id);
    }

    @Override
    public External2OrderDTO getById(String id) {
        External2OrderDO external2OrderDO = external2OrderDAO.getById(id);
        return external2OrderManagerConvert.do2dto(external2OrderDO);
    }

    @Override
    public External2OrderDTO getByOrderCode(String orderCode) {
        External2OrderDO external2OrderDO = external2OrderDAO.getByOrderCode(orderCode);
        return external2OrderManagerConvert.do2dto(external2OrderDO);
    }
}
