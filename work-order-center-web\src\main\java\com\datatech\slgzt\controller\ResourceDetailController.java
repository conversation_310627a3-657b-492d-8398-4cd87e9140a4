package com.datatech.slgzt.controller;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.ResourceDetailWebConvert;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.CustomDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.model.req.resource.ResourceDetailDetailReq;
import com.datatech.slgzt.model.req.resource.ResourceDetailPageReq;
import com.datatech.slgzt.model.req.resource.ResourceMountGetReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateIdReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.recovery.ResourceMountGetReqVO;
import com.datatech.slgzt.model.vo.resource.NetworkDetailVO;
import com.datatech.slgzt.model.vo.resource.ResourceDetailVO;
import com.datatech.slgzt.service.ManagerViewService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月15日 19:25:06
 */
@RestController
@RequestMapping("/resource")
public class ResourceDetailController {


    @Resource
    private ResourceDetailManager resourceDetailManager;


    @Resource
    private TenantManager tenantManager;

    @Resource
    private ManagerViewService managerViewService;


    @Resource
    private ResourceDetailWebConvert convert;


    /**
     * page
     */
    @RequestMapping("/page")
    public CommonResult<PageResult<ResourceDetailVO>> page(@RequestBody ResourceDetailPageReq req) {
        ResourceDetailQuery query = convert.convert(req);
        //产品固定只能提供给当前登录用户自己的创建申请的才能看到 默认带一个用户id
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))) {
            //获取当前用户角色
            List<Long> tenantIds = tenantManager.listTenIdByOwnerId(currentUserId);
            if (ObjNullUtils.isNull(tenantIds)) {
                return CommonResult.success(new PageResult<>());
            }
            query.setTenantList(tenantIds);
        }
        PageResult<ResourceDetailDTO> page = resourceDetailManager.page(query);
        PageResult<ResourceDetailVO> box = PageWarppers.box(page, convert::convert);
        box.getRecords().forEach(resourceDetailVO -> {

        });
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    /**
     * page
     */
    @RequestMapping("/pageCorporate")
    public CommonResult<PageResult<ResourceDetailVO>> pageCorporate(@RequestBody ResourceDetailPageReq req) {
        ResourceDetailQuery query = convert.convert(req);
        //产品固定只能提供给当前登录用户自己的创建申请的才能看到 默认带一个用户id
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))) {
            //获取当前用户角色
            List<Long> tenantIds = tenantManager.listRelTenantIdByUserId(currentUserId);
            if (ObjNullUtils.isNull(tenantIds)) {
                return CommonResult.success(new PageResult<>());
            }
            query.setTenantList(tenantIds);
        }
        if (query.getDisOrderStatus() == null) {
            query.setDisOrderStatus("0");
        }
        PageResult<ResourceDetailDTO> page = resourceDetailManager.page(query);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    @RequestMapping("/generateCorporateOrder")
    public CommonResult<Void> generateCorporateOrder(@RequestBody ResourceDetailPageReq req) {
        ResourceDetailQuery query = convert.convert(req);
        if (query.getDisOrderStatus() == null) {
            query.setDisOrderStatus("0");
        }
        resourceDetailManager.generateCorporateOrder(query);
        return CommonResult.success(null);
    }

    @RequestMapping("/pageCustom")
    public CommonResult<PageResult<ResourceDetailVO>> pageCustom(@RequestBody ResourceDetailPageReq req) {
        ResourceDetailQuery query = convert.convert(req);
        CustomDTO customDTO = managerViewService.getCustomDetail(req.getCustomId());
        //产品固定只能提供给当前登录用户自己的创建申请的才能看到 默认带一个用户id
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
//        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
//        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
//        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))) {
////            //获取当前用户角色
////            List<Long> tenantIds = tenantManager.listRelTenantIdByUserId(currentUserId);
////            if (ObjNullUtils.isNull(tenantIds)) {
////                return CommonResult.success(new PageResult<>());
////            }
//            List<Long> tenantIds = customDTO.getTenantId() == null ?
//                    Collections.emptyList() :
//                    Arrays.stream(customDTO.getTenantId().split(","))
//                            .map(Long::valueOf)
//                            .collect(Collectors.toList());
//            query.setTenantList(tenantIds);
//        }
        List<Long> tenantIds = customDTO.getTenantId() == null ?
                Collections.emptyList() :
                Arrays.stream(customDTO.getTenantId().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
        query.setTenantList(tenantIds);
        PageResult<ResourceDetailDTO> page = resourceDetailManager.page(query);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    @RequestMapping("/countCustom")
    public CommonResult<Map<String, Long>> countCustom(@RequestBody ResourceDetailPageReq req) {
        ResourceDetailQuery query = convert.convert(req);
        CustomDTO customDTO = managerViewService.getCustomDetail(req.getCustomId());
        //产品固定只能提供给当前登录用户自己的创建申请的才能看到 默认带一个用户id
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<Long> tenantIds = customDTO.getTenantId() == null ?
                Collections.emptyList() :
                Arrays.stream(customDTO.getTenantId().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
        query.setTenantList(tenantIds);
        query.setSourceType(null);
        query.setSourceTypeList(ListUtil.toList("DG", "FB"));
        List<ResourceDetailDTO> list = resourceDetailManager.list(query);
        Map<String, Long> typeCountMap = list.stream()
                .filter(Objects::nonNull) // 过滤null对象
                .collect(Collectors.groupingBy(
                        ResourceDetailDTO::getType, // 按type分组
                        Collectors.counting()      // 统计数量
                ));
        return CommonResult.success(typeCountMap);
    }


    @RequestMapping("/detail")
    public CommonResult<ResourceDetailVO> detail(@RequestBody ResourceDetailDetailReq req) {
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(req.getId());
        if (ObjNullUtils.isNull(resourceDetailDTO)) {
            return null;
        }
        return CommonResult.success(convert.convert(resourceDetailDTO));
    }


    /**
     * 云主机/gpu云主机查看网络详情
     * @param query
     * @return
     */
    @RequestMapping("/getNetWorkDetail")
    public CommonResult<List<NetworkDetailVO>> getNetWorkDetail(@RequestBody VmOperateQuery query) {
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(query.getId());
        if (StringUtils.isBlank(resourceDetailDTO.getNetworkModelSnapshot())){
            return CommonResult.success(new ArrayList<>());
        }
        String networkModelSnapshot = resourceDetailDTO.getNetworkModelSnapshot();
        List<PlaneNetworkModel> networkList = JSONObject.parseArray(networkModelSnapshot, PlaneNetworkModel.class);
        List<NetworkDetailVO> list = Lists.newArrayList();
        for (PlaneNetworkModel planeNetworkModel : networkList) {
            String type = "网络";
            String[] str = planeNetworkModel.getId().split("_");
            if (NetworkPrefixEnum.VPC.getType().equals(str[0])) {
                type = "VPC";
            }
            for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                NetworkDetailVO networkDetailVO = new NetworkDetailVO();
                networkDetailVO.setType(type);
                networkDetailVO.setName(planeNetworkModel.getName());
                networkDetailVO.setPlan(planeNetworkModel.getPlane());
                networkDetailVO.setCidr(subnet.getSubnetIp());
                networkDetailVO.setSubnetName(subnet.getSubnetName());
                if (StringUtils.isNotBlank(subnet.getBusinessIpv4())) {
                    networkDetailVO.setIpv4(subnet.getBusinessIpv4());
                }
                if (StringUtils.isNotBlank(subnet.getBusinessIpv6())) {
                    networkDetailVO.setIpv6(subnet.getBusinessIpv6());
                }
                if (StringUtils.isNotBlank(subnet.getAddressIpv4())) {
                    if (StringUtils.isEmpty(networkDetailVO.getIpv4())) {
                        networkDetailVO.setIpv4(subnet.getAddressIpv4());
                    } else {
                        networkDetailVO.setIpv4(networkDetailVO.getIpv4() + "," + subnet.getAddressIpv4());
                    }
                }
                if (StringUtils.isNotBlank(subnet.getAddressIpv6())) {
                    if (StringUtils.isEmpty(networkDetailVO.getIpv6())) {
                        networkDetailVO.setIpv6(subnet.getAddressIpv6());
                    } else {
                        networkDetailVO.setIpv6(networkDetailVO.getIpv6() + "," + subnet.getAddressIpv6());
                    }
                }
                if (StringUtils.isNotBlank(subnet.getManageIpv4())) {
                    if (StringUtils.isEmpty(networkDetailVO.getIpv4())) {
                        networkDetailVO.setIpv4(subnet.getManageIpv4());
                    } else {
                        networkDetailVO.setIpv4(networkDetailVO.getIpv4() + "," + subnet.getManageIpv4());
                    }
                }
                if (StringUtils.isNotBlank(subnet.getManageIpv6())) {
                    if (StringUtils.isEmpty(networkDetailVO.getIpv6())) {
                        networkDetailVO.setIpv6(subnet.getManageIpv6());
                    } else {
                        networkDetailVO.setIpv6(networkDetailVO.getIpv6() + "," + subnet.getManageIpv6());
                    }
                }
                list.add(networkDetailVO);
            }
        }
        return CommonResult.success(list);
    }

    /**
     * 查询网卡信息
     *
     * @param instanceUuid 实例uuid
     * @param ip           ip地址
     * @return
     */
    @RequestMapping("/queryNet")
    public CommonResult<NetcardDetailDTO> queryNet(@RequestParam String instanceUuid, @RequestParam String ip) {
        return CommonResult.success(resourceDetailManager.selectNetInfoByUuidAndIp(instanceUuid, ip));
    }

    /**
     * 操作云主机
     *
     * @param operate
     * @return
     */
    @RequestMapping("/vm/operate")
    @OperationLog(description = "操作云主机", operationType = "UPDATE")
    public CommonResult operateVm(@RequestBody VmOperateQuery operate) {
        return resourceDetailManager.operateVm(operate);
    }


    /**
     * 获取获取额外挂载的
     */
    @RequestMapping(value = "/getMountResource", method = RequestMethod.POST)
    public CommonResult<List<ResourceMountGetReqVO>> getMountResource(@RequestBody ResourceMountGetReq req) {
        Precondition.checkArgument(req.getResourceDetailIdList(), "资源id不能为空");
        ArrayList<ResourceMountGetReqVO> resultList = Lists.newArrayList();
        if (req.getType().equals("ecs") || req.getType().equals("gcs")) {
            req.getResourceDetailIdList().forEach(resourceDetailId -> {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(resourceDetailId);
                //先查询evs有没有挂载
                List<ResourceDetailDTO> list =
                        resourceDetailManager.list(new ResourceDetailQuery().setVmId(resourceDetailDTO.getDeviceId()));
                List<ResourceDetailVO> resourceDetailVOS = StreamUtils.mapArray(list, convert::convert);
                if (resourceDetailVOS.isEmpty()) {
                    return;
                }
                ResourceMountGetReqVO resourceMountGetReqVO = new ResourceMountGetReqVO();
                resourceMountGetReqVO.setResourceDetailId(resourceDetailId);
                resourceMountGetReqVO.setName(resourceDetailDTO.getDeviceName());
                resourceMountGetReqVO.setResourceDetailVOList(resourceDetailVOS);
                resultList.add(resourceMountGetReqVO);
            });
        }
        return CommonResult.success(resultList);

    }
}
