package com.datatech.slgzt.model.req.external2;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class External2OrderProductCreateReq {

    @NotBlank(message = "工单ID不能为空")
    private String workOrderId;

    @NotBlank(message = "产品类型不能为空")
    private String productType;

    @NotBlank(message = "属性快照不能为空")
    private String propertySnapshot;

    private Long parentProductId;

    private String gid;

    private String ext;

    private Long subOrderId;

    private Long jobExecutionId;
} 