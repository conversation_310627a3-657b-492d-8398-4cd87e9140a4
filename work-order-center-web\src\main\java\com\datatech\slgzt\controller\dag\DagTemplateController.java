package com.datatech.slgzt.controller.dag;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.DagTemplateWebConvert;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.DagTemplateManager;
import com.datatech.slgzt.manager.PlatformInterfaceManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.dto.PlatformInterfaceDTO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.model.req.dag.*;
import com.datatech.slgzt.model.req.slb.SlbCertificateIdReq;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.dag.DagTemplateVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * DAG模板控制器
 */
@RestController
@RequestMapping("/dagTemplate")
public class DagTemplateController {

    @Resource
    private DagTemplateManager dagTemplateManager;

    @Resource
    private DagTemplateWebConvert dagTemplateWebConvert;

    @Resource
    private PlatformInterfaceManager platformInterfaceManager;

    /**
     * 创建DAG模板
     */
    @PostMapping("/create")
    public CommonResult<Void> create(@Valid @RequestBody DagTemplateCreateReq req) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        DagTemplateDTO dto = dagTemplateWebConvert.req2dto(req);
        dto.setCreator(currentUser.getUserName());
        dagTemplateManager.create(dto);
        // vmware
        PlatformInterfaceDTO dto1 = new PlatformInterfaceDTO();
        dto1.setId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
        dto1.setDomainCode(CatalogueDomain.VMWARE.getCode());
        // 资源中心是小写的
        dto1.setDomainName("融合边缘云-Vmware");
        dto1.setResType("dag");
        dto1.setResName("产品组合编排");
        dto1.setUrl("/dagTemplate/create");
        dto1.setUrlNote("产品组合编排-" + req.getName() + " 创建");
        dto1.setStatus("1");
        dto1.setSort(0);
        platformInterfaceManager.insert(dto1);
        // 华为
        dto1.setId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
        dto1.setDomainCode(CatalogueDomain.HUAWEI.getCode());
        dto1.setDomainName(CatalogueDomain.HUAWEI.getName());
        platformInterfaceManager.insert(dto1);
        // 浪潮
        dto1.setId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
        dto1.setDomainCode(CatalogueDomain.INSPUR.getCode());
        dto1.setDomainName(CatalogueDomain.INSPUR.getName());
        platformInterfaceManager.insert(dto1);
        // 华三
        dto1.setId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
        dto1.setDomainCode(CatalogueDomain.H3C.getCode());
        dto1.setDomainName(CatalogueDomain.H3C.getName());
        platformInterfaceManager.insert(dto1);
        return CommonResult.success();
    }

    /**
     * 创建DAG模板产品
     */
    @PostMapping("/updateRegion")
    @OperationLog(description = "更新DAG模板产品资源池", operationType = "CREATE")
    public CommonResult<Void> updateRegion(@RequestBody DagTemplateProductCreateReq req) {
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "模板ID不能为空");
        DagTemplateDTO dagTemplateDTO = dagTemplateWebConvert.req2dto(req);
        dagTemplateManager.update(dagTemplateDTO);
        return CommonResult.success();
    }

    /**
     * copy
     */
    @PostMapping("/copy")
    public CommonResult<Void> copy(@RequestBody SlbCertificateIdReq req) {
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "模板ID不能为空");
        DagTemplateDTO dagTemplateDTO = dagTemplateManager.getById(req.getId());
        Precondition.checkArgument(dagTemplateDTO != null, "模板不存在");
        dagTemplateDTO.setId(null);
        dagTemplateDTO.setCreateTime(null);
        dagTemplateDTO.setReginCodeList(null);
        dagTemplateDTO.setCatalogueDomainCodeList(null);
        dagTemplateDTO.setDomainCodeList(null);
        dagTemplateManager.create(dagTemplateDTO);
        return CommonResult.success();
    }

    /**
     * 更新DAG模板
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@Valid @RequestBody DagTemplateUpdateReq req) {
        DagTemplateDTO dto = dagTemplateWebConvert.req2dto(req);
        dagTemplateManager.update(dto);
        return CommonResult.success();
    }

    /**
     * 删除DAG模板
     */
    @PostMapping("/delete")
    public CommonResult<Void> delete(@Valid @RequestBody DagTemplateGetReq req) {
        DagTemplateDTO dto = dagTemplateManager.getById(req.getId());
        dagTemplateManager.delete(req.getId());
        if (dto != null) {
            platformInterfaceManager.delete("产品组合编排-" + dto.getName() + " 创建");
        }
        return CommonResult.success();
    }

    /**
     * 查询DAG模板列表
     */
    @PostMapping("/list")
    public CommonResult<List<DagTemplateVO>> list(@Valid @RequestBody DagTemplateQuery query) {
        List<DagTemplateDTO> list = dagTemplateManager.list(query);
        return CommonResult.success(list.stream()
                .map(dagTemplateWebConvert::dto2vo)
                .collect(Collectors.toList()));
    }

    /**
     * 分页查询DAG模板
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DagTemplateVO>> page(@RequestBody DagTemplatePageReq req) {
        // 转换请求对象为查询对象
        DagTemplateQuery query = dagTemplateWebConvert.req2query(req);
        // 执行分页查询
        PageResult<DagTemplateDTO> result = dagTemplateManager.page(query);
        return CommonResult.success(PageWarppers.box(result, dagTemplateWebConvert::dto2vo));
    }

    /**
     * detail
     */
    @PostMapping("/detail")
    public CommonResult<DagTemplateVO> detail(@RequestBody DagTemplateGetReq req) {
        Precondition.checkArgument(req.getId() != null, "模板ID不能为空");
        DagTemplateDTO dagTemplateDTO = dagTemplateManager.getById(req.getId());
        Precondition.checkArgument(dagTemplateDTO != null, "模板不存在");
        DagTemplateVO vo = dagTemplateWebConvert.dto2vo(dagTemplateDTO);
        return CommonResult.success(vo);
    }
} 