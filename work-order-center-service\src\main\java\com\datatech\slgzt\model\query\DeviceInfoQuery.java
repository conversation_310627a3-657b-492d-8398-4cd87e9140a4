package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Desc 显卡搜索条件
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Data
@Accessors(chain = true)
public class DeviceInfoQuery {

    private int pageSize = 10;

    private int pageNum;

    private String areaCode;


    private String deviceType;

    private String inUsed;

    private String dncIp;


    //是否管理
    private String inManage;


    /**
     * 云类型编码
     */
    private String catalogueDomainCode;


    //来源类型
    private String sourceType;


    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private Long regionId;

    /**
     * 资源池Code
     */
    private String regionCode;


    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime  endTime;
    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    private List<String> deviceIds;

    private String deviceId;

    /**
     * 显卡型号
     */
    private String modelName;


    //算力排序
    private Boolean gpuSort = false;

    private List<String> physicalDeviceIdList;

    private String sliceStatus;

    private String deptName;

    private String businessSystemName;

    private String subModelName;

    private List<String> areaCodes;

    private List<String> modelNames;
}
