package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.convert.External2OrderServiceConvert;
import com.datatech.slgzt.dao.mapper.TenantMapper;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.manager.External2OrderManager;
import com.datatech.slgzt.manager.External2OrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.External2OrderCreateOpm;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.service.external2.External2OrderService;
import com.datatech.slgzt.service.producer.SmsProducer;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.utils.UuidUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月09日 11:18:01
 */
@Service
@Slf4j
public class External2OrderServiceImpl implements External2OrderService {

    ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private External2OrderManager orderManager;

    @Resource
    private External2OrderProductManager productManager;

    @Resource
    private SmsProducer smsProducer;

    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private ProductGeneralCheckService productGeneralCheckService;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;


    @Resource
    private External2OrderServiceConvert convert;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TenantMapper tenantMapper;


    @SneakyThrows
    @Override
    public void createOrder(External2OrderCreateOpm opm) {
        convert.fillBase(opm);
        //校验
        ProductGeneralCheckOpm checkOpm = convert.convertCheck(opm);
        productGeneralCheckService.checkImageExist(checkOpm);
        productGeneralCheckService.checkFlavorExist(checkOpm);
        productGeneralCheckService.checkProductInResourcePool(checkOpm);
        productGeneralCheckService.checkProductInResourcePoolCapacity(checkOpm);
        Precondition.checkArgument(ObjNullUtils.isNull(checkOpm.getErrorMessages()), checkOpm.getErrorMessages()
                .toString());
        //创建订单
        External2OrderDTO orderDTO = new External2OrderDTO();
        orderDTO.setId(OrderTypeEnum.EXTERNAL.getPrefix() + '-' + IdUtil.nanoId());
        orderDTO.setOrderCode(OrderTypeEnum.EXTERNAL.getPrefix() + System.currentTimeMillis());
        orderDTO.setCreateBy(opm.getCreateBy());
        orderDTO.setCreateByName(opm.getCreateByName());
        orderDTO.setTenantId(opm.getTenantId());
        orderDTO.setTenantName(opm.getTenantName());
        orderDTO.setBusinessSystemId(opm.getBusinessSystemId());
        orderDTO.setBusinessSystemName(opm.getBusinessSystemName());
        orderDTO.setOrderAmount(opm.getAmount());
        String orderId = orderManager.insert(orderDTO);
        //-----------------------ecs----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getEcsModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------redis----------------------------------------
        StreamUtils.mapArray(opm.getRedisModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------mysql----------------------------------------
        StreamUtils.mapArray(opm.getMysqlModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------postgreSql----------------------------------------
        StreamUtils.mapArray(opm.getPostgreSqlModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------gcs----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getGcsModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------mysql----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getRdsMysqlModelList(), Function.identity()).forEach(item -> {
            checkFillRdsMysqlResource(item, orderId);
        });
        //-----------------------nat----------------------------------------
        StreamUtils.mapArray(opm.getNatModelList(), Function.identity()).forEach(item -> {
            checkFillNatResource(item, orderId);
        });
        //-----------------------slb----------------------------------------
        StreamUtils.mapArray(opm.getSlbModelList(), Function.identity()).forEach(item -> {
            checkFillSlbResource(item, orderId);
        });
        //-----------------------evs----------------------------------------
        StreamUtils.mapArray(opm.getEvsModelList(), Function.identity()).forEach(item -> {
            checkFillEvsResource(item, orderId);
        });
        //-----------------------eip----------------------------------------
        StreamUtils.mapArray(opm.getEipModelList(), Function.identity()).forEach(item -> {
            checkFillEipResource(item, orderId);
        });
        //-----------------------obs----------------------------------------
        StreamUtils.mapArray(opm.getObsModelList(), Function.identity()).forEach(item -> {
            checkFillObsResource(item, orderId);
        });
        //-----------------------cloudPort----------------------------------------
        StreamUtils.mapArray(opm.getCloudPortModelList(), Function.identity()).forEach(item -> {
            checkFillCloudPortResource(item, orderId);
        });
        //-----------------------vpn----------------------------------------
        StreamUtils.mapArray(opm.getVpnModelList(), Function.identity()).forEach(item -> {
            checkFillVpnResource(item, orderId);
        });
        //------------------------backup----------------------------------------
        StreamUtils.mapArray(opm.getBackupModelList(), Function.identity()).forEach(item -> {
            checkFillBackupResource(item, orderId);
        });
        //开启流程
        Long jobId = executor.submit(() -> {
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("orderId", orderId)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();
            //第一次同步执行必然会失败的 主要是为了返回id
            return jobLauncher.run(jobRegistry.getJob("external2ProductCreateJob"), jobParameters)
                    .getId();
        }).get();
        //存入jobId
        External2OrderDTO orderDbDTO = orderManager.getById(orderId);
        orderDbDTO.setJobExecutionId(jobId);
        orderManager.update(orderDbDTO);
//        if (!opm.getDirectSubmit()) {
//            corporateOrderTempSaveService.handleDeleteAll(opm.getTenantId().toString());
//            List<String> usersToBeDeleted = corporateOrderTempSaveService.getUsersToBeDeleted(opm.getTenantId().toString());
//            //调用删除用户的方法
//            UserHelper.INSTANCE.delUserByIds(StreamUtils.toLong(usersToBeDeleted));
//        }
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, orderId, KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(jobId)
                .setRestartOnly(true)));

    }

    @Override
    public void restart(String id) {
        External2OrderDTO orderDTO = orderManager.getById(id);
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, id, KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(orderDTO.getJobExecutionId())
                .setRestartOnly(true)));
    }

    private void checkFillObsResource(ObsModel model, String workOrderId) {
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getObsName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setObsName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.OBS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    private void checkFillEipResource(EipModel model, String workOrderId) {
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getEipName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }


    private void checkFillEvsResource(EvsModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    private void checkFillRdsMysqlResource(MysqlV2Model model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        //按照主产品的开通数量设置好Id数
        String originalVmName = model.getMysqlName(); // 保存原始名称
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setMainIds(mainIds);
            model.setId(mainId);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setOriginName(originalVmName);
            model.setMysqlName(originalVmName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.MYSQL.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }


    private void checkFillSlbResource(SlbModel model, String workOrderId) {
        Precondition.checkArgument(model.getSlbName(), "slbName不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getSlbName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setSlbName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.SLB.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }


    private void checkFillNatResource(NatGatwayModel model, String workOrderId) {
        Precondition.checkArgument(model.getNatName(), "natName不能为空");
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getNatName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setNatName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.NAT.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }

    private void checkFillEscResource(EcsModel model, String workOrderId) {
        //如果存在挂载数据盘 需要校验挂载数据盘的内容
        if (model.getMountDataDisk()) {
            List<EvsModel> mountDataDiskList = model.getMountDataDiskList();
            Precondition.checkArgument(mountDataDiskList, "mountDataDiskList不能为空");
            for (EvsModel evsModel : mountDataDiskList) {
                Precondition.checkArgument(evsModel.getSysDiskSize(), "挂载数据盘的系统盘大小不能为空");
                Precondition.checkArgument(evsModel.getSysDiskType(), "挂载数据盘的系统盘类型不能为空");
                evsModel.setAzCode(model.getAzCode());
                evsModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setMountDataDiskList(null);
        }
        Precondition.checkArgument(model.getBindPublicIp(), "bindPublicIp不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        Precondition.checkArgument(model.getApplyTime(), "applyTime不能为空");
        Precondition.checkArgument(model.getOpenNum(), "openNum不能为空");
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0 && model.getOpenNum() < 201, "openNum必须大于0并且小于200");
        //数据都没问题了后创建数据
        //循环开通数量 ---下面部分需要抽离公用的方法
        // 循环创建指定数量的主产品
        //按照主产品的开通数量设置好Id数
        String originalVmName = model.getVmName(); // 保存原始名称
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品（ESC）
            String productType = model.getProductType();
            model.setMainIds(mainIds);
            model.setId(mainId);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setOriginName(originalVmName);
            model.setVmName(originalVmName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(productType));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            // 创建SSD子产品
            createEvsProduct(model.getMountDataDiskList(), mainId, workOrderId);
            // 创建EIP子产品
            createEipProduct(model.getEipModelList(), mainId, workOrderId);
        }
    }

    private void checkFillBackupResource(BackupModel model, String workOrderId) {
        //并且大于0
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.VPN.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }

    }

    private void checkFillVpnResource(VpnModel model, String workOrderId) {
        //校验vpc下是否已经创建了vpn
        //如果有未回收的vpn（根据资源表判断）报错
        PlaneNetworkModel planeNetworkModel = model.getPlaneNetworkModel();
        Precondition.checkArgument(planeNetworkModel, "网络信息不能为空");
        String vpcId = planeNetworkModel.getId();
        List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery().setVpcId(vpcId).setType("vpn"));
        Precondition.checkArgument(CollectionUtil.isEmpty(list), "该vpc已经绑定了vpn资源");
        //vmware的vpn回收后7天再释放，没超过7天的报错
        if (DomainCodeEnum.PLF_PROV_MOC_ZJ_VMWARE.getCode().equals(model.getDomainCode())) {
            RBucket<String> bucket = redissonClient.getBucket("vpn:recovery:" + vpcId);
            Precondition.checkArgument(Objects.isNull(bucket.get()), "回收时间未超过7天，vpn资源暂未释放，无法创建");
        }
        //并且大于0
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.VPN.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }


    private void checkFillCloudPortResource(CloudPortModel model, String workOrderId) {
        //并且大于0
        List<Long> mainIds = generateMainIds(1);
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.CLOUDPORT.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }


    private void createEvsProduct(List<EvsModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        for (EvsModel model : models) {
            // 对共业务，云主机中的evs，支持输入数量
            int count = model.getOpenNum() == null ? 1 : model.getOpenNum();
            for (int i = 0; i < count; i++) {
                long id = IdUtil.getSnowflake().nextId();
                model.setId(id);
                External2OrderProductDTO product = new External2OrderProductDTO();
                product.setId(id);
                product.setProductType(ProductTypeEnum.EVS.getCode());
                product.setOrderId(workOrderId);
                product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
                product.setPropertySnapshot(JSON.toJSONString(model));
                product.setParentProductId(parentId);
                product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
                productManager.insert(product);
            }
        }
    }

    private void createEipProduct(List<EipModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            External2OrderProductDTO product = new External2OrderProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            return product;
        }).forEach(productManager::insert);
    }

    private List<Long> generateMainIds(int openNum) {
        List<Long> mainIds = Lists.newArrayListWithCapacity(openNum);
        for (int i = 0; i < openNum; i++) {
            mainIds.add(IdUtil.getSnowflake().nextId());
        }
        return mainIds;
    }


    @Override
    public void sendFailSms(Long productId) {
        External2OrderProductDTO productDTO = productManager.getById(productId);
        //获取工单信息
        External2OrderDTO orderDTO = orderManager.getById(productDTO.getOrderId());

        // 1.租户
        Long createBy = orderDTO.getCreateBy();
        // 2.客户经理
        // 如果什么都查询不到 也要调用当做删除了
        Map<String, String> data = tenantMapper.getByTenantId(orderDTO.getTenantId());
        if (data.isEmpty()) {
            log.info("没有查到对应的集团客户信息,租户id:{}", orderDTO.getTenantId());
            return;
        }
        String phone = data.get("C_MANAGER_CONTACT_PHONE");
        // 3.小芸（固定的人）
        List<String> roleList =
                Lists.newArrayList(AuthorityCodeEnum.XIAOYUN.code());

        //内容
        SmsSendModel dgFail = new SmsSendModel()
                .setUserId(createBy)
                .setRoles(roleList)
                .setOrderCode(orderDTO.getOrderCode())
                .setOrderType("dg_fail")
                .setProductType(ProductTypeEnum.getByCode(productDTO.getProductType()).getDesc());
        if (StringUtils.isNotBlank(phone)) {
            dgFail.setPhone(phone);
        }
        smsProducer.sendMessage(createBy.toString(), dgFail);
    }
}
