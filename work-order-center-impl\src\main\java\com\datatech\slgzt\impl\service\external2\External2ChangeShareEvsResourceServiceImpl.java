package com.datatech.slgzt.impl.service.external2;

import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 共享云硬盘变更服务实现类
 * <AUTHOR>
 * @description 共享云硬盘变更逻辑，与EVS云硬盘变更逻辑基本一致，但需要设置共享标识
 * @date 2025年09月27日 18:50:05
 */
@Slf4j
@Service
public class External2ChangeShareEvsResourceServiceImpl extends External2ChangeEvsResourceServiceImpl {
    /**
     * 注册共享云硬盘变更服务
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.SHARE_EVS;
    }
}
