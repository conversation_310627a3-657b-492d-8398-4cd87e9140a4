package com.datatech.slgzt.model.opm;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 外部2回收工单创建OPM
 */
@Data
public class External2RecoveryOrderCreateOpm {
    private List<String> recoveryDeviceIdList = new ArrayList<>();

    //这里的id都给资源表的goodsOrderId好了

    //回收ecsId列表
    private List<Long> ecsIdList;

    //回收gcsId列表
    private List<Long> gcsIdList;

    //回收mysqlId列表
    private List<Long> rdsMysqlIdList;

    //回收redisId列表
    private List<Long> redisIdList;

    //回收evs列表
    private List<Long> evsIdList;

    //回收evs列表
    private List<Long> shareEvsIdList;

    //回收eip列表
    private List<Long> eipIdList;

    //回收nat列表
    private List<Long> natIdList;

    //回收obs列表
    private List<Long> obsIdList;

    //回收slb列表
    private List<Long> slbIdList;

    //vpc网络回收id列表
    private List<String> vpcIdList;

    //网络回收id列表
    private List<String> networkIdList;

    //回收云备份列表
    private List<Long> backupIdList;

    //回收vpn列表
    private List<Long> vpnIdList;

    //回收nas列表
    private List<Long> nasIdList;

    //回收裸金属列表
    private List<Long> pmIdList;

    //同步回收列表
    private List<Long> syncRecoveryIdList;

    //草稿判断
    private Boolean canDraft = false;

    /**
     * 创建人名称
     */
    private String createdUserName;

    //创建的用户id
    private Long createUserId;

    // 回收类型。0-默认，1-用户注销。默认0。
    private Integer recoveryType;

    /**
     * 用户注销时存在
     */
    private String billId;

    private Long tenantId;

    private String tenantName;
}
