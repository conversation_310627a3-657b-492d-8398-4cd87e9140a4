package com.datatech.slgzt.service.external2;

import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.opm.External2RecoveryOrderCreateOpm;
import com.datatech.slgzt.model.query.External2RecoveryOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface External2RecoveryOrderService {


    /**
     * createRecoveryWorkOrder
     */
    String createRecoveryWorkOrder(External2RecoveryOrderCreateOpm opm);

    PageResult<External2RecoveryOrderDTO> page(External2RecoveryOrderQuery query, Long currentUserId);

    /**
     * 填充创建工单的校验信息
     * 主要是设备和网络直接的校验
     *
     * @param opm
     */
    void fillCheckCreate(External2RecoveryOrderCreateOpm opm);

    void recovery(String orderId);

    void restart(String id);

    /**
     * 取消工单中的某些产品
     *
     * @param productIds 产品id
     */
    void cancel(List<Long> productIds);
}
