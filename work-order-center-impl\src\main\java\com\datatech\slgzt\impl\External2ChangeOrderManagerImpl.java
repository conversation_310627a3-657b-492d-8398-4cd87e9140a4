package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.External2ChangeOrderManagerConvert;
import com.datatech.slgzt.dao.External2ChangeOrderDAO;
import com.datatech.slgzt.dao.model.order.External2ChangeOrderDO;
import com.datatech.slgzt.manager.External2ChangeOrderManager;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部2变更工单Manager实现类
 */
@Service
public class External2ChangeOrderManagerImpl implements External2ChangeOrderManager {

    @Autowired
    private External2ChangeOrderDAO external2ChangeOrderDAO;

    @Autowired
    private External2ChangeOrderManagerConvert external2ChangeOrderManagerConvert;

    @Override
    public void add(External2ChangeOrderDTO dto) {
        External2ChangeOrderDO external2ChangeOrderDO = external2ChangeOrderManagerConvert.dto2do(dto);
        external2ChangeOrderDAO.insert(external2ChangeOrderDO);
    }

    @Override
    public void update(External2ChangeOrderDTO dto) {
        External2ChangeOrderDO external2ChangeOrderDO = external2ChangeOrderManagerConvert.dto2do(dto);
        external2ChangeOrderDAO.update(external2ChangeOrderDO);
    }

    @Override
    public void delete(String id) {
        external2ChangeOrderDAO.delete(id);
    }

    @Override
    public External2ChangeOrderDTO getById(String id) {
        External2ChangeOrderDO external2ChangeOrderDO = external2ChangeOrderDAO.getById(id);
        return external2ChangeOrderManagerConvert.do2dto(external2ChangeOrderDO);
    }

    @Override
    public PageResult<External2ChangeOrderDTO> page(External2ChangeOrderQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<External2ChangeOrderDO> list = external2ChangeOrderDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), external2ChangeOrderManagerConvert::do2dto);
    }

    @Override
    public List<External2ChangeOrderDTO> list(External2ChangeOrderQuery query) {
        List<External2ChangeOrderDO> list = external2ChangeOrderDAO.list(query);
        return list.stream()
                .map(external2ChangeOrderManagerConvert::do2dto)
                .collect(Collectors.toList());
    }
}