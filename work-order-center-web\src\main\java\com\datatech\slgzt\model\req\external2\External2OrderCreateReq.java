package com.datatech.slgzt.model.req.external2;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class External2OrderCreateReq {

    //租户id
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    //直接提交订单
    private Boolean directSubmit = Boolean.TRUE;

    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<MysqlV2Model> rdsMysqlModelList;

    private List<EcsModel> redisModelList;

    /**
     * mysql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<EcsModel> mysqlModelList;

    /**
     * postgreSql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<EcsModel> postgreSqlModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * 共享evs申请资源列表的json
     */
    private List<EvsModel> shareEvsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 容器资源配额申请json
     */
    private List<CQModel> cqModelList;

    /**
     * 备份策略申请json
     */
    private List<BackupModel> backupModelList;

    /**
     * 云端口
     */
    private List<CloudPortModel> cloudPortModelList;

    /**
     * vpn
     */
    private List<VpnModel> vpnModelList;

} 