package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DeviceGpuInfoMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Desc DeviceGpuInfoDAO
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Repository
public class DeviceGpuInfoDAO {

    @Resource
    private DeviceGpuInfoMapper mapper;

    public DeviceGpuInfoDO getById(String id) {
        return mapper.selectById(id);
    }

    public DeviceGpuInfoDO getByDeviceId(String deviceId) {
        return StreamUtils.findAny(mapper.selectList(Wrappers.<DeviceGpuInfoDO>lambdaQuery()
                                                     .like(DeviceGpuInfoDO::getDeviceId, deviceId)));
    }

    public void insert(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.insert(deviceGpuInfoDO);
    }


    public List<String> groupModelName(){
        return mapper.groupModelName();
    }

    public List<String> groupDeptName(String modelName, String subModelName){
        List<String> modelNames = Lists.newArrayList();
        if (ObjNullUtils.isNotNull(modelName)){
            //按照逗号分隔
             modelNames = Arrays.asList(modelName.split(","));
        }
        return mapper.groupDeptName(modelNames);
    }

    public List<String> groupBusinessSystemName(String modelName, String subModelName){
        List<String> modelNames = Lists.newArrayList();
        if (ObjNullUtils.isNotNull(modelName)){
            //按照逗号分隔
             modelNames = Arrays.asList(modelName.split(","));
        }
        return mapper.groupBusinessSystemName(modelNames);
    }


    public void updateById(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.updateById(deviceGpuInfoDO);
    }

    public void updateLastByDeviceId(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.update(null, Wrappers.<DeviceGpuInfoDO>lambdaUpdate()
                                    .set(DeviceGpuInfoDO::getLastPeriod, deviceGpuInfoDO.getLastPeriod())
                                    .like(DeviceGpuInfoDO::getDeviceId, deviceGpuInfoDO.getDeviceId()));
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    public List<DeviceGpuInfoDO> selectDeviceGpuInfoList(DeviceGpuInfoDTO query){
        return mapper.selectList(Wrappers.<DeviceGpuInfoDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), DeviceGpuInfoDO::getDomainCode, query.getDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), DeviceGpuInfoDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getRegionCode()), DeviceGpuInfoDO::getRegionCode, query.getRegionCode())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DeviceGpuInfoDO::getBusinessSystemId, query.getBusinessSystemId()));
    }

    public List<DeviceGpuInfoDO> selectList(DeviceInfoQuery query) {
        LambdaQueryWrapper<DeviceGpuInfoDO> wrapper = Wrappers.<DeviceGpuInfoDO>lambdaQuery()

                                                              .eq(ObjNullUtils.isNotNull(query.getAreaCode()), DeviceGpuInfoDO::getAreaCode, query.getAreaCode())
                                                              .eq(ObjNullUtils.isNotNull(query.getSourceType()), DeviceGpuInfoDO::getSourceType, query.getSourceType())
                                                              .eq(ObjNullUtils.isNotNull(query.getSliceStatus()), DeviceGpuInfoDO::getSliceStatus, query.getSliceStatus())
                                                              .eq(ObjNullUtils.isNotNull(query.getInUsed()), DeviceGpuInfoDO::getInUsed, query.getInUsed())
                                                              .eq(ObjNullUtils.isNotNull(query.getInManage()), DeviceGpuInfoDO::getInManage, query.getInManage())
                                                              .eq(ObjNullUtils.isNotNull(query.getDncIp()), DeviceGpuInfoDO::getDcnNetAddr, query.getDncIp())
                                                              .eq(ObjNullUtils.isNotNull(query.getDeviceType()), DeviceGpuInfoDO::getDeviceType, query.getDeviceType())
                                                              .eq(ObjNullUtils.isNotNull(query.getDomainCode()), DeviceGpuInfoDO::getDomainCode, query.getDomainCode())
                                                              .eq(ObjNullUtils.isNotNull(query.getModelName()), DeviceGpuInfoDO::getModelName, query.getModelName())
                                                              .in(ObjNullUtils.isNotNull(query.getDeviceIds()), DeviceGpuInfoDO::getDeviceId, query.getDeviceIds())
                                                              .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), DeviceGpuInfoDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                                                              .eq(ObjNullUtils.isNotNull(query.getRegionCode()), DeviceGpuInfoDO::getRegionCode, query.getRegionCode())
                                                              .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DeviceGpuInfoDO::getBusinessSystemId, query.getBusinessSystemId())
                                                              .eq(ObjNullUtils.isNotNull(query.getDeptName()), DeviceGpuInfoDO::getDeptName, query.getDeptName())
                                                              .eq(ObjNullUtils.isNotNull(query.getBusinessSystemName()), DeviceGpuInfoDO::getBusinessSystemName, query.getBusinessSystemName())
                                                              .eq(ObjNullUtils.isNotNull(query.getSubModelName()), DeviceGpuInfoDO::getSubModelName, query.getSubModelName())
                                                              .isNotNull(ObjNullUtils.isNotNull(query.getGpuSort()) && query.getGpuSort(), DeviceGpuInfoDO::getLastPeriod)
                                                              .in(ObjNullUtils.isNotNull(query.getAreaCodes()), DeviceGpuInfoDO::getAreaCode, query.getAreaCodes())
                                                              .last(ObjNullUtils.isNotNull(query.getGpuSort()) && query.getGpuSort(),
                                                                      "order by CAST(COALESCE(json_value(LAST_PERIOD, '$.gpuUtilPercent'), '-1') AS DECIMAL) DESC");

        if (ObjNullUtils.isNotNull(query.getModelNames())) {
            wrapper.and(q -> q.in(DeviceGpuInfoDO::getModelName, query.getModelNames())
                              .or()
                              .in(DeviceGpuInfoDO::getSubModelName, query.getModelNames()));
        }

        return mapper.selectList(wrapper);
    }

    // 提取为独立方法，返回 SQL 片段
    private String handleSubModelNameCondition(DeviceInfoQuery query) {
        boolean contains910B = isModelNamesContains910B(query.getModelNames());

        if (!contains910B) {
            if (query.getSubModelName() != null) {
                return "SUB_MODEL_NAME = '" + query.getSubModelName() + "'";
            }
            // 不加条件
            return "1=1";
        }

        // 包含 910B
        if (query.getSubModelName() == null) {
            return "(SUB_MODEL_NAME IN ('910B2', '910B4') OR SUB_MODEL_NAME IS NULL)";
        } else {
            List<String> specialValues = Arrays.asList("910B2", "910B4");
            if (specialValues.contains(query.getSubModelName())) {
                return "SUB_MODEL_NAME = '" + query.getSubModelName() + "'";
            } else {
                return "SUB_MODEL_NAME IS NULL";
            }
        }
    }

    /**
     * 检查modelNames是否包含910B
     */
    private boolean isModelNamesContains910B(List<String> modelNames) {
        if (modelNames == null || modelNames.isEmpty()) {
            return false;
        }
        return modelNames.stream()
                .anyMatch(model -> "910B".equals(model.trim()));
    }

}
