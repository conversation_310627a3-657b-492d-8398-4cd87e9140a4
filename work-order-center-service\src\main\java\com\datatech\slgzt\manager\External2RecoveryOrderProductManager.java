package com.datatech.slgzt.manager;

import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.model.dto.External2RecoveryOrderProductDTO;

import java.util.List;

public interface External2RecoveryOrderProductManager {

    void insert(External2RecoveryOrderProductDTO productDTO);


    External2RecoveryOrderProductDTO getById(Long id);

    void update(External2RecoveryOrderProductDTO productDTO);


    List<External2RecoveryOrderProductDTO> getByIds(List<Long> ids);

    List<External2RecoveryOrderProductDTO> listByWorkOrderId(String workOrderId);

    List<External2RecoveryOrderProductDTO> listByResourceDetailId(String resourceDetailId, RecoveryStatusEnum recoveryStatus);

    List<External2RecoveryOrderProductDTO> listChildren(Long id);

    void updateStatusByParentId(Long id, Integer status);

    External2RecoveryOrderProductDTO getBySubOrderId(Long subOrderId);

    void updateHcmByCmdbIds(List<String> configIds, String status);

    void updateHcmByIds(List<Long> configIds, String status);

    void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm);

    void deleteByWorkOrderId(String workOrderId);

    External2RecoveryOrderProductDTO getByCmdbId(String cmdbId);

    void updateStatusByIds(List<Long> ids, Integer status);

    void deleteById(Long id);
}
