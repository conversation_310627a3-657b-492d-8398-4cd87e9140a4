package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2RecoveryOrderProductMapper;
import com.datatech.slgzt.dao.model.order.External2RecoveryOrderProductDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2回收工单产品DAO
 */
@Repository
public class External2RecoveryOrderProductDAO {

    @Resource
    private External2RecoveryOrderProductMapper mapper;


    public void insert(External2RecoveryOrderProductDO productDO) {
        mapper.insert(productDO);
    }

    public External2RecoveryOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    public void update(External2RecoveryOrderProductDO productDO) {
        mapper.updateById(productDO);
    }


    public List<External2RecoveryOrderProductDO> getByIds(List<Long> ids) {
        return mapper.selectBatchIds(ids);
    }

    /**
     * listByWorkOrderId
     */
    public List<External2RecoveryOrderProductDO> listByWorkOrderId(String workOrderId) {
        return mapper.selectList(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery()
                .eq(External2RecoveryOrderProductDO::getWorkOrderId, workOrderId));
    }

    public List<External2RecoveryOrderProductDO> listByResourceDetailId(String resourceDetailId, Integer recoveryStatus) {
        return mapper.selectList(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery()
                .eq(External2RecoveryOrderProductDO::getResourceDetailId, resourceDetailId)
                .eq(External2RecoveryOrderProductDO::getRecoveryStatus, recoveryStatus));
    }

    public List<External2RecoveryOrderProductDO> listChildren(Long id) {
        return mapper.selectList(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery()
                .eq(External2RecoveryOrderProductDO::getParentProductId, id));
    }

    public void updateByParentId(External2RecoveryOrderProductDO productDO) {
        mapper.update(productDO, Wrappers.<External2RecoveryOrderProductDO>lambdaUpdate()
                .eq(External2RecoveryOrderProductDO::getParentProductId
                        , productDO.getParentProductId()));
    }

    public External2RecoveryOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery().eq(External2RecoveryOrderProductDO::getSubOrderId, subOrderId));
    }

    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        External2RecoveryOrderProductDO productDO = new External2RecoveryOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<External2RecoveryOrderProductDO>lambdaUpdate()
                .in(External2RecoveryOrderProductDO::getCmdbId
                        , configIds));
    }

    public void updateHcmByIds(List<Long> ids, String status) {
        External2RecoveryOrderProductDO productDO = new External2RecoveryOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<External2RecoveryOrderProductDO>lambdaUpdate()
                .in(External2RecoveryOrderProductDO::getId
                        , ids));
    }

    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        External2RecoveryOrderProductDO productDO = new External2RecoveryOrderProductDO();
        productDO.setTenantConfirm(tenantConfirm);
        mapper.update(productDO, Wrappers.<External2RecoveryOrderProductDO>lambdaUpdate()
                .in(External2RecoveryOrderProductDO::getId, ids));
    }

    public void deleteByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery()
                .eq(External2RecoveryOrderProductDO::getWorkOrderId, workOrderId));
    }

    public External2RecoveryOrderProductDO getByCmdbId(String cmdbId) {
        return mapper.selectOne(Wrappers.<External2RecoveryOrderProductDO>lambdaQuery()
                .eq(External2RecoveryOrderProductDO::getCmdbId, cmdbId));
    }

    public void updateStatusByIds(List<Long> ids, Integer status) {
        External2RecoveryOrderProductDO productDO = new External2RecoveryOrderProductDO();
        productDO.setRecoveryStatus(status);
        mapper.update(productDO, Wrappers.<External2RecoveryOrderProductDO>lambdaUpdate()
                .in(External2RecoveryOrderProductDO::getId, ids));
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }
}
