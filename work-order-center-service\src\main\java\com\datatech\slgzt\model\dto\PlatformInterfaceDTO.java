package com.datatech.slgzt.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 13:56:13
 */
@Data
public class PlatformInterfaceDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 平台code
     */
    private String domainCode;

    /**
     * 平台名称
     */
    private String domainName;

    /**
     * 资源类型
     */
    private String resType;

    private String resName;

    /**
     * 接口地址
     */
    private String url;

    /**
     * 接口名称
     */
    private String urlNote;

    /**
     * 是否生效1-生效，0-未生效
     */
    private String status;

    private Integer sort;

}
