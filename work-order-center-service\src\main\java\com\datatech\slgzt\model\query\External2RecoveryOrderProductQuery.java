package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 外部2回收工单产品查询对象
 */
@Data
@Accessors(chain = true)
public class External2RecoveryOrderProductQuery {

    /** 工单ID */
    private String workOrderId;

    /** 产品类型 */
    private String productType;

    /** 父类产品ID */
    private Long parentProductId;

    /** GID */
    private String gid;

    /** 子订单ID */
    private Long subOrderId;

    /** 回收状态 */
    private String recoveryStatus;

    /** 资源详情ID */
    private String resourceDetailId;

    /** ID列表 */
    private List<Long> idList;

    /** 工单ID列表 */
    private List<String> workOrderIdList;

    /** 页码 */
    private Integer pageNum = 1;

    /** 每页大小 */
    private Integer pageSize = 10;
}
