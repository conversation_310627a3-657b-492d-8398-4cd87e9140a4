package com.datatech.slgzt.impl.manager;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.convert.External2RecoveryOrderManagerConvert;
import com.datatech.slgzt.dao.External2RecoveryOrderDAO;
import com.datatech.slgzt.dao.model.order.External2RecoveryOrderDO;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.manager.External2RecoveryOrderManager;
import com.datatech.slgzt.model.dto.External2RecoveryOrderDTO;
import com.datatech.slgzt.model.query.External2RecoveryOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2回收工单Manager实现类
 */
@Service
public class External2RecoveryOrderManagerImpl implements External2RecoveryOrderManager {

    @Resource
    private External2RecoveryOrderDAO dao;

    @Resource
    private External2RecoveryOrderManagerConvert convert;


    @Override
    public String createWorkOrder(External2RecoveryOrderDTO reconveryWorkOrderDTO) {
        if (ObjNullUtils.isNull(reconveryWorkOrderDTO.getId())) {
            reconveryWorkOrderDTO.setId(OrderTypeEnum.EXTERNAL2_RECOVERY.getPrefix() + "-" + IdUtil.nanoId());
        }
        dao.insert(convert.dto2do(reconveryWorkOrderDTO));
        return reconveryWorkOrderDTO.getId();
    }

    @Override
    public PageResult<External2RecoveryOrderDTO> page(External2RecoveryOrderQuery query) {
        if (query.getPageNum() != null && query.getPageSize() != null) {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
        }
        List<External2RecoveryOrderDO> orderDOS = dao.list(query);
        return PageWarppers.box(new PageInfo<>(orderDOS), convert::do2dto);
    }

    @Override
    public External2RecoveryOrderDTO getById(String id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public void update(External2RecoveryOrderDTO orderDTO) {
        External2RecoveryOrderDO orderDO = convert.dto2do(orderDTO);
        dao.update(orderDO);
    }

    @Override
    public List<External2RecoveryOrderDTO> list(External2RecoveryOrderQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }
}
