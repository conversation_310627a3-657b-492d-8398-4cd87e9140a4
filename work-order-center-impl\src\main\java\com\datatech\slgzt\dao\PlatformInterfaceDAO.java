package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datatech.slgzt.dao.mapper.PlatformInterfaceMapper;
import com.datatech.slgzt.dao.model.PlatformInterfaceDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 13:53:46
 */
@Repository
public class PlatformInterfaceDAO {


    @Resource
    private PlatformInterfaceMapper mapper;


    public void insert(PlatformInterfaceDO platformInterfaceDO) {
        mapper.insert(platformInterfaceDO);
    }

    public void delete(String name) {
        LambdaUpdateWrapper<PlatformInterfaceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PlatformInterfaceDO::getUrlNote, name);
        updateWrapper.set(PlatformInterfaceDO::getStatus, 0);
        mapper.update(null, updateWrapper);
    }

}
