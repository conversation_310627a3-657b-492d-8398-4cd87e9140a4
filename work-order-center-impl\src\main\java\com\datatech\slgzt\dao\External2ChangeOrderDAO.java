package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2ChangeOrderMapper;
import com.datatech.slgzt.dao.model.order.External2ChangeOrderDO;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部2变更工单DAO
 */
@Repository
public class External2ChangeOrderDAO {

    @Resource
    private External2ChangeOrderMapper external2ChangeOrderMapper;

    public void insert(External2ChangeOrderDO external2ChangeOrderDO) {
        external2ChangeOrderMapper.insert(external2ChangeOrderDO);
    }

    public void update(External2ChangeOrderDO external2ChangeOrderDO) {
        external2ChangeOrderMapper.updateById(external2ChangeOrderDO);
    }

    public void delete(String id) {
        external2ChangeOrderMapper.deleteById(id);
    }

    public External2ChangeOrderDO getById(String id) {
        return external2ChangeOrderMapper.selectById(id);
    }

    public List<External2ChangeOrderDO> list(External2ChangeOrderQuery query) {
        return external2ChangeOrderMapper.selectList(
                Wrappers.<External2ChangeOrderDO>lambdaQuery()
                        .like(ObjNullUtils.isNotNull(query.getOrderCode()), External2ChangeOrderDO::getOrderCode, query.getOrderCode())
                        .eq(ObjNullUtils.isNotNull(query.getTenantId()), External2ChangeOrderDO::getTenantId, query.getTenantId())
                        .like(StringUtils.isNotBlank(query.getTenantName()), External2ChangeOrderDO::getTenantName, query.getTenantName())
                        .like(StringUtils.isNotBlank(query.getCreator()), External2ChangeOrderDO::getCreator, query.getCreator())
                        .eq(ObjNullUtils.isNotNull(query.getCreatorId()), External2ChangeOrderDO::getCreatorId, query.getCreatorId())
                        .eq(ObjNullUtils.isNotNull(query.getJobExecutionId()), External2ChangeOrderDO::getJobExecutionId, query.getJobExecutionId())
                        .in(ObjNullUtils.isNotNull(query.getIdList()), External2ChangeOrderDO::getId, query.getIdList())
                        .in(ObjNullUtils.isNotNull(query.getTenantIds()), External2ChangeOrderDO::getTenantId, query.getTenantIds())
                        .between(
                                ObjNullUtils.isNotNull(query.getCreateTimeStart()) && ObjNullUtils.isNotNull(query.getCreateTimeEnd()),
                                External2ChangeOrderDO::getCreateTime,
                                query.getCreateTimeStart(),
                                query.getCreateTimeEnd()
                        )
                        .orderByDesc(External2ChangeOrderDO::getCreateTime)
        );
    }
}
