package com.datatech.slgzt.controller.external2;

import com.datatech.slgzt.convert.External2OrderWebConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.External2OrderManager;
import com.datatech.slgzt.manager.External2OrderProductManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.opm.External2OrderCreateOpm;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.datatech.slgzt.model.query.External2OrderQuery;
import com.datatech.slgzt.model.req.external2.External2OrderCreateReq;
import com.datatech.slgzt.model.req.external2.External2OrderDetailReq;
import com.datatech.slgzt.model.req.external2.External2OrderPageReq;
import com.datatech.slgzt.model.req.external2.External2OrderPriceCalculateReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.external2.External2OrderDetailVO;
import com.datatech.slgzt.model.vo.external2.External2OrderVO;
import com.datatech.slgzt.service.ResourcePriceService;
import com.datatech.slgzt.service.external2.External2OrderService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/external2/order")
public class External2OrderController {

    @Resource
    private External2OrderManager orderManager;

    @Resource
    private External2OrderProductManager productManager;

    @Resource
    private External2OrderService orderService;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private ResourcePriceService resourcePriceService;

    @Resource
    private External2OrderWebConvert convert;

    @PostMapping("/page")
    public CommonResult<PageResult<External2OrderVO>> page(@RequestBody External2OrderPageReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageNum()), "页码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageSize()), "每页大小不能为空");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "当前用户未登录");
        List<Long> tenantIdList = tenantManager.listRelTenantIdByUserId(currentUser.getId());
        External2OrderQuery query = convert.convert(req);
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))
                && !roles.contains("general_admin")) {
            query.setTenantIds(tenantIdList);
        }
        PageResult<External2OrderDTO> page = orderManager.page(query);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    @PostMapping("/detail")
    public CommonResult<External2OrderDetailVO> detail(@RequestBody External2OrderDetailReq req) {
        List<External2OrderProductDTO> list = productManager.list(new External2OrderProductQuery()
                .setOrderId(req.getId())
                .setParentId(0L));
        return CommonResult.success(convert.convert(list));
    }


    @PostMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> create(@RequestBody External2OrderCreateReq req) {
        // 参数校验
        //获取当前登录用户信息
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "用户信息不能为空");
        //通过登录用户获取用户的业务系统
        TenantDTO tenantDTO = tenantManager.getById(req.getTenantId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(tenantDTO), "用户没有租户");
        CmpAppDTO cmpAppDTO = businessService.selectByTenantId(tenantDTO.getId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(cmpAppDTO), "用户没有业务系统");
        External2OrderCreateOpm opm = convert.convert2Opm(req);
        opm.setBusinessSystemId(cmpAppDTO.getSystemId());
        opm.setBusinessSystemName(cmpAppDTO.getSystemName());
        opm.setTenantId(tenantDTO.getId());
        opm.setTenantName(tenantDTO.getName());
        opm.setBillId(tenantDTO.getBillId());
        opm.setCustomNo(tenantDTO.getCustomNo());
        opm.setCreateBy(currentUser.getId());
        opm.setCreateByName(currentUser.getUserName());
        orderService.createOrder(opm);
        return CommonResult.success();
    }

    //restart
    @PostMapping("/restart")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> restart(@RequestBody External2OrderDetailReq req) {
        orderService.restart(req.getId());
        return CommonResult.success();

    }

    /**
     * 价格计算
     */
    @RequestMapping(value = "/priceCalculate", method = RequestMethod.POST)
    public CommonResult<BigDecimal> priceCalculate(@RequestBody External2OrderPriceCalculateReq req) {
        // 参数校验
        //获取当前登录用户信息
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "用户信息不能为空");
        //通过登录用户获取用户的业务系统
        String productType = req.getProductType();
        BigDecimal price = null;
        switch (productType) {
            case "ecs":
                price = resourcePriceService.calculateVmPrice(req.getEcsModel());
                break;
            case "gcs":
                price = resourcePriceService.calculateVmPrice(req.getGcsModel());
                break;
            case "mysql":
                price = resourcePriceService.calculateVmPrice(req.getMysqlModel());
                break;
            case "postgreSql":
                price = resourcePriceService.calculateVmPrice(req.getPostgreSqlModel());
                break;
            case "redis":
                price = resourcePriceService.calculateVmPrice(req.getRedisModel());
                break;
            case "evs":
                price = resourcePriceService.calculateVolumePrice(req.getEvsModel());
                break;
            case "nat":
                price = resourcePriceService.calculateNatPrice(req.getNatModel());
                break;
            case "slb":
                price = resourcePriceService.calculateSlbPrice(req.getSlbModel());
                break;
            case "obs":
                price = resourcePriceService.calculateObsPrice(req.getObsModel());
                break;
            case "vpn":
                price = resourcePriceService.calculateVpnPrice(req.getVpnModel());
                break;
        }
        return CommonResult.success(price);
    }

}