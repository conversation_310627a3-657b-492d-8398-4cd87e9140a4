package com.datatech.slgzt.model.vo.external2;

import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class External2RecoveryWorkOrderDetailVO {
    private String id;

    /**
     * 订单标题
     */
    private String orderTitle;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 计费号
     */
    private String billId;


    /**
     * 客户编码
     */
    private String customNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 创建者id
     */
    @JsonProperty("createdBy")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @JsonProperty("createdUserName")
    private String creator;

    /**
     * 修改者id
     */
    private Long updatedBy;

    /**
     * 三级业务部门领导ID
     */
    private Long levelThreeLeaderId;

    /**
     * 流程实例ID
     */
    private String activitiId;

    /**
     * 流程模板KEY
     */
    private String activiteKey;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 开始角色
     */
    private String startRoleCode;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 商品类型，资源类型：ecs 云主机、evs 云硬盘、gcs GPU云主机、obs 对象存储、slb 负载均衡、nat NAT网关；网络类型：vpc VPC、network 网络
     */
    private String recoveryType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 订单状态 0 删除 1 正常
     */
    private Integer STATUS;

    /**
     * 当前流程所属节点
     */
    private String currentNodeCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 业务系统id
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    private String departmentName;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    /**
     * 所属业务模块 todo 后续添加
     */
    private String moduleName;

    //创建的用户id
    private Long createUserId;

    //审批记录
    private List<WorkOrderAuthLogDTO> approveRecordList;

    //当前activity流程
    private ActivityTaskVo activityTask;

    /**
     * 当前流程节点名称
     */
    private String currentNodeName;

    // ---------------资源产品列表-----------------------

    private List<ResourceDetailDTO> ecsList;
    private List<ResourceDetailDTO> gcsList;
    private List<ResourceDetailDTO> rdsMysqlList;
    private List<ResourceDetailDTO> redisList;
    private List<ResourceDetailDTO> evsList;
    private List<ResourceDetailDTO> eipList;
    private List<ResourceDetailDTO> slbList;
    private List<ResourceDetailDTO> obsList;
    private List<ResourceDetailDTO> natList;
    private List<VpcOrderResult> vpcList;
    private List<NetworkOrderResult> networkList;
    private List<ResourceDetailDTO> backupList;
    private List<ResourceDetailDTO> vpnList;
    private List<ResourceDetailDTO> nasList;
    private List<ResourceDetailDTO> pmList;
    private List<ResourceDetailDTO> kafkaList;
    private List<ResourceDetailDTO> flinkList;
    private List<ResourceDetailDTO> esList;


    private List<RecoveryEcsModel> ecsListExt;
    private List<RecoveryEcsModel> gcsListExt;
    private List<RecoveryMysqlV2Model> rdsMysqlListExt;
    private List<RecoveryEcsModel> redisListExt;
    private List<RecoveryEvsModel> evsListExt;
    private List<RecoveryEvsModel> shareEvsListExt;
    private List<RecoveryEipModel> eipListExt;
    private List<RecoverySlbModel> slbListExt;
    private List<RecoveryObsModel> obsListExt;
    private List<RecoveryNatModel> natListExt;
    private List<RecoveryVpcModel> vpcListExt;
    private List<RecoveryNetworkModel> networkListExt;
    private List<RecoveryBackupModel> backupListExt;
    private List<RecoveryVpnModel> vpnListExt;
    private List<RecoveryNasModel> nasListExt;
    private List<RecoveryPmModel> pmListExt;
    private List<RecoveryKafkaModel> kafkaListExt;
    private List<RecoveryFlinkModel> flinkListExt;
    private List<RecoveryEsModel> esListExt;

}
