package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.External2OrderCreateOpm;
import com.datatech.slgzt.model.query.External2OrderQuery;
import com.datatech.slgzt.model.req.external2.External2OrderCreateReq;
import com.datatech.slgzt.model.req.external2.External2OrderPageReq;
import com.datatech.slgzt.model.req.external2.External2OrderUpdateReq;
import com.datatech.slgzt.model.vo.external2.External2OrderDetailVO;
import com.datatech.slgzt.model.vo.external2.External2OrderVO;
import com.datatech.slgzt.utils.HashUtils;
import com.google.common.collect.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface External2OrderWebConvert {

    External2OrderQuery convert(External2OrderPageReq req);

    External2OrderDTO convert(External2OrderCreateReq req);

    External2OrderDTO convert(External2OrderUpdateReq req);

    External2OrderVO convert(External2OrderDTO dto);


    @Mapping(target = "slbModelList", source = "slbModelList", qualifiedByName = "mapSlbModelList")
    @Mapping(target = "natModelList", source = "natModelList", qualifiedByName = "mapNatModelList")
    @Mapping(target = "ecsModelList", source = "ecsModelList", qualifiedByName = "mapEcsModelList")
    @Mapping(target = "gcsModelList", source = "gcsModelList", qualifiedByName = "mapGcsModelList")
    //gcs不需要转换，特殊业务需要造成Gcs的对象都在ECS里
    @Mapping(target = "redisModelList", source = "redisModelList", qualifiedByName = "mapRedisModelList")
    @Mapping(target = "mysqlModelList", source = "mysqlModelList", qualifiedByName = "mapMysqlModelList")
    @Mapping(target = "postgreSqlModelList", source = "postgreSqlModelList", qualifiedByName = "mapPostgreSqlModelList")
    External2OrderCreateOpm convert2Opm(External2OrderCreateReq req);


    @Named("mapSlbModelList")
    default List<SlbModel> mapSlbModelList(List<SlbModel> slbModelList) {
        if (slbModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        slbModelList.forEach(slbModel -> slbModel.setFlavorCode(slbModel.getFlavorId()));
        return slbModelList;
    }

    @Named("mapNatModelList")
    default List<NatGatwayModel> mapNatModelList(List<NatGatwayModel> natModelList) {
        if (natModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        natModelList.forEach(natModel -> natModel.setFlavorCode(natModel.getFlavorId()));
        return natModelList;
    }

    @Named("mapRedisModelList")
    default List<EcsModel> mapRedisModelList(List<EcsModel> ecsModelList) {
        if (ecsModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        ecsModelList.forEach(ecsModel -> ecsModel.setFlavorCode(ecsModel.getFlavorId()));
        return ecsModelList;
    }

    @Named("mapMysqlModelList")
    default List<EcsModel> mapMysqlModelList(List<EcsModel> ecsModelList) {
        if (ecsModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        ecsModelList.forEach(ecsModel -> ecsModel.setFlavorCode(ecsModel.getFlavorId()));
        return ecsModelList;
    }

    @Named("mapPostgreSqlModelList")
    default List<EcsModel> mapPostgreSqlModelList(List<EcsModel> ecsModelList) {
        if (ecsModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        ecsModelList.forEach(ecsModel -> ecsModel.setFlavorCode(ecsModel.getFlavorId()));
        return ecsModelList;
    }

    @Named("mapEcsModelList")
    default List<CloudEcsResourceModel> mapEcsModelList(List<CloudEcsResourceModel> ecsModelList) {
        if (ecsModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        ecsModelList.forEach(ecsModel -> ecsModel.setFlavorCode(ecsModel.getFlavorId()));
        return ecsModelList;
    }

    @Named("mapGcsModelList")
    default List<CpuEcsResourceModel> mapGcsModelList(List<CpuEcsResourceModel> gcsModelList) {
        if (gcsModelList == null) {
            return null;
        }
        // 遍历列表，将 floveCode 赋值给 floveId
        gcsModelList.forEach(gcsModel -> gcsModel.setFlavorCode(gcsModel.getFlavorId()));
        return gcsModelList;
    }


    default External2OrderDetailVO convert(List<External2OrderProductDTO> list) {
        External2OrderDetailVO vo = new External2OrderDetailVO();
        List<CloudEcsResourceModel> ecsList = Lists.newArrayList();
        List<CpuEcsResourceModel> gcsList = Lists.newArrayList();
        List<MysqlV2Model> mysqlList = Lists.newArrayList();
        List<EcsModel> redisList = Lists.newArrayList();
        List<EcsModel> postgreSqlList = Lists.newArrayList();
        List<EcsModel> redisModelList = Lists.newArrayList();
        List<EvsModel> evsList = Lists.newArrayList();
        List<EvsModel> shareEvsModelList = Lists.newArrayList();
        List<EipModel> eipModelList = Lists.newArrayList();
        List<SlbModel> slbModelList = Lists.newArrayList();
        List<ObsModel> obsModels = Lists.newArrayList();
        List<NatGatwayModel> natGatwayModels = Lists.newArrayList();
        List<BackupModel> backModels = Lists.newArrayList();
        List<VpnModel> vpnModels = Lists.newArrayList();
        // 处理ESC类型
        processProductType(list, ProductTypeEnum.ECS.getCode(), CloudEcsResourceModel.class, ecsList);
        vo.setEcsModelList(ecsList);
        // 处理GCS类型
        processProductType(list, ProductTypeEnum.GCS.getCode(), CpuEcsResourceModel.class, gcsList);
//        gcsList = StreamUtils.distinctByKey(gcsList, CpuEcsResourceModel::getIdHash);
        vo.setGcsModelList(gcsList);
        // 处理mysql类型
        processProductType(list, ProductTypeEnum.RDS_MYSQL.getCode(), MysqlV2Model.class, mysqlList);
//        mysqlList = StreamUtils.distinctByKey(mysqlList, MysqlV2Model::getIdHash);
        vo.setRdsMysqlModelList(mysqlList);
        // 处理redis类型
        processProductType(list, ProductTypeEnum.REDIS.getCode(), EcsModel.class, redisList);
//        redisList = StreamUtils.distinctByKey(redisList, EcsModel::getIdHash);
        vo.setRedisModelList(redisList);
        // 处理postgreSql类型
        processProductType(list, ProductTypeEnum.POSTGRESQL.getCode(), EcsModel.class, postgreSqlList);
        vo.setPostgreSqlModelList(postgreSqlList);
        // 处理mysql类型
        processProductType(list, ProductTypeEnum.MYSQL.getCode(), EcsModel.class, redisModelList);
        vo.setMysqlModelList(redisModelList);
        // 处理EVS类型
        processProductType(list, ProductTypeEnum.EVS.getCode(), EvsModel.class, evsList);
//        evsList = StreamUtils.distinctByKey(evsList, EvsModel::getIdHash);
        vo.setEvsModelList(evsList);
        // 处理shareEVS类型
        processProductType(list, ProductTypeEnum.SHARE_EVS.getCode(), EvsModel.class, shareEvsModelList);
//        evsList = StreamUtils.distinctByKey(evsList, EvsModel::getIdHash);
        vo.setShareEvsModelList(shareEvsModelList);
        // 处理SLB类型
        processProductType(list, ProductTypeEnum.SLB.getCode(), SlbModel.class, slbModelList);
//        slbModelList = StreamUtils.distinctByKey(slbModelList, SlbModel::getIdHash);
        vo.setSlbModelList(slbModelList);
        // 处理eip类型
        processProductType(list, ProductTypeEnum.EIP.getCode(), EipModel.class, eipModelList);
//        eipModelList = StreamUtils.distinctByKey(eipModelList, EipModel::getIdHash);
        vo.setEipModelList(eipModelList);
        // 处理OBS类型
        processProductType(list, ProductTypeEnum.OBS.getCode(), ObsModel.class, obsModels);
//        obsModels = StreamUtils.distinctByKey(obsModels, ObsModel::getIdHash);
        vo.setObsModelList(obsModels);
        // 处理NAT类型
        processProductType(list, ProductTypeEnum.NAT.getCode(), NatGatwayModel.class, natGatwayModels);
//        natGatwayModels = StreamUtils.distinctByKey(natGatwayModels, NatGatwayModel::getIdHash);
        vo.setNatModelList(natGatwayModels);
        // 处理BACKUP类型
        processProductType(list, ProductTypeEnum.BACKUP.getCode(), BackupModel.class, backModels);
//        backModels = StreamUtils.distinctByKey(backModels, BackupModel::getIdHash);
        vo.setBackupModelList(backModels);
        // 处理vpn类型
        processProductType(list, ProductTypeEnum.VPN.getCode(), VpnModel.class, vpnModels);
//        vpnModels = StreamUtils.distinctByKey(vpnModels, VpnModel::getIdHash);
        vo.setVpnModelList(vpnModels);
        return vo;
    }


    // 泛型方法处理公共逻辑
    default <T> void processProductType(List<External2OrderProductDTO> mainProducts,
                                        String productType,
                                        Class<T> targetClass,
                                        List<? super T> resultList) {
        mainProducts.stream()
                .filter(item -> productType.equals(item.getProductType()))
                .forEach(item -> {
                    T resourceModel = JSON.parseObject(item.getPropertySnapshot(), targetClass);
                    String idHash = HashUtils.calculateUniqueId(
                            ((BaseProductModel) resourceModel).getMainIds()
                    );
                    ((BaseProductModel) resourceModel).setIdHash(idHash);
                    ((BaseProductModel) resourceModel).setMessage(item.getMessage());
                    ((BaseProductModel) resourceModel).setStatus(item.getOpenStatus());
                    resultList.add(resourceModel);
                });
    }
}