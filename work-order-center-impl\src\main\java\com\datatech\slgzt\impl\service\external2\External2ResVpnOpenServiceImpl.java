package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.DomainCodeEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.External2OrderManager;
import com.datatech.slgzt.manager.External2OrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.External2OrderDTO;
import com.datatech.slgzt.model.dto.External2OrderProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.nostander.VpnModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.external2.External2ResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @program: workordercenterproject
 * @description: vpn开通
 * @author: LK
 * @create: 2025-06-10 10:15
 **/
@Service
@Slf4j
public class External2ResVpnOpenServiceImpl implements External2ResOpenService {

    @Resource
    private External2OrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private External2OrderManager orderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private RedissonClient redissonClient;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public Object openResource(External2OrderProductDTO productDTO) {
        //获取工单
        External2OrderDTO orderDTO = orderManager.getById(productDTO.getOrderId());
        VpnModel vpnModel = JSON.parseObject(productDTO.getPropertySnapshot(), VpnModel.class);
        //校验vpc下是否已经创建了vpn
        //如果有未回收的vpn（根据资源表判断）报错
        PlaneNetworkModel planeNetworkModel = vpnModel.getPlaneNetworkModel();
        Precondition.checkArgument(planeNetworkModel, "网络信息不能为空");
        String vpcId = planeNetworkModel.getId();
        List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery().setVpcId(vpcId).setType("vpn"));
        Precondition.checkArgument(CollectionUtil.isEmpty(list), "该vpc已经绑定了vpn资源");
        //vmware的vpn回收后7天再释放，没超过7天的报错
        if (DomainCodeEnum.PLF_PROV_MOC_ZJ_VMWARE.getCode().equals(vpnModel.getDomainCode())) {
            RBucket<String> bucket = redissonClient.getBucket("vpn:recovery:" + vpcId);
            Precondition.checkArgument(Objects.isNull(bucket.get()), "回收时间未超过7天，vpn资源暂未释放，无法创建");
        }
        Long tenantId = platformService.getOrCreateTenantId(vpnModel.getBillId(), vpnModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(vpnModel.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.EXTERNAL2.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("VPN_CREATE");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(vpnModel.getBusinessSystemId().toString());
        //设置客户id
        resOpenReqModel.setCustomId(vpnModel.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(vpnModel.getRegionCode());
        //设置的是主产品的gid 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        resOpenReqModel.setGid(productDTO.getGid());
        //设置租户id
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreateBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(8);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList = Lists.newArrayList();
        //------------设置vpn的参数-----------------------------------------------------
        List<ResOpenReqModel.ProductOrder> natProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.VPN).assembleParam(new ResOpenOpm()
                .setTenantId(tenantId)
                .setAccount(vpnModel.getBillId())
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setGId(productDTO.getGid())
                .setVpnModelList(Lists.newArrayList(vpnModel)));
        reqProductList.addAll(natProduct);

        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={},request param={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl, JSON.toJSONString(resOpenReqModel));
        Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
        log.info("资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
        return null;
    }


    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.VPN;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
