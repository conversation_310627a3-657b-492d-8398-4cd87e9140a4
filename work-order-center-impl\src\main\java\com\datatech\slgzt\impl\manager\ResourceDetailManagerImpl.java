package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.ResourceDetailManagerConvert;
import com.datatech.slgzt.dao.ResourceDetailDAO;
import com.datatech.slgzt.dao.VnicDAO;
import com.datatech.slgzt.dao.WocSecurityGroupDAO;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.dao.model.VnicDO;
import com.datatech.slgzt.dao.model.security.WocSecurityGroupDO;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 13:00:18
 */
@Service
public class ResourceDetailManagerImpl implements ResourceDetailManager {

    @Resource
    private ResourceDetailDAO dao;

    @Resource
    private WocSecurityGroupDAO securityGroupDao;

    @Resource
    private VnicDAO vnicDAO;

    @Resource
    private ResourceDetailManagerConvert convert;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Resource
    private CorporateOrderProductManager corporateOrderProductManager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutVmOperate = "v1/erm/vm/operate";
    private final String layoutVmResetPwd = "v1/erm/vm/resetPwd";


    @Override
    public ResourceDetailDTO selectByIdNoStatus(Long id) {
        return convert.do2dto(dao.selectByIdNoStatus(id));
    }

    /**
     * saveResourceDetail
     *
     * @param resourceDetailVO
     */
    @Override
    public Long saveResourceDetail(ResourceDetailDTO dto) {
        ResourceDetailDO detailDO = convert.dto2do(dto);
        dao.insert(detailDO);
        return detailDO.getId();
    }

    @Override
    public void updateById(ResourceDetailDTO dt) {
        dao.updateById(convert.dto2do(dt));
    }

    @Override
    public void updateVmIdAndEscNameById(ResourceDetailDTO dt) {
        dao.updateVmIdAndEscNameById(dt.getId(), dt.getDeviceId(), dt.getDeviceName(), dt.getRecoveryStatus());
    }

    @Override
    public void updateVmIdAndEscNameById2(Long id, String vmId, String ecsName, Integer recoveryStatus, String mountOrNot) {
        dao.updateVmIdAndEscNameById2(id, vmId, ecsName, recoveryStatus,mountOrNot);
    }
    @Override
    public void updateShareVolumeInfoById(Long id, String shareVolumeId, String shareDataDisk) {
        dao.updateShareVolumeInfoById(id, shareVolumeId, shareDataDisk);
    }

    @Override
    public void updateEipById(ResourceDetailDTO dt) {
        dao.updateEipById(dt.getId(), dt.getRecoveryStatus());
    }

    @Override
    public void updateSecurityGroupIdById(ResourceDetailDTO dt) {
        dao.updateSecurityGroupIdById(dt.getId(), dt.getSecurityGroupIds());
    }

    @Override
    public void updateRecoveryTypeByIds(List<Long> ids, Integer status) {
        dao.updateRecoveryTypeByIds(ids, status);
    }

    @Override
    public void updateRecoveryTypeByDeviceIds(List<String> deviceIds, Integer status) {
        dao.updateRecoveryTypeByDeviceIds(deviceIds, status);
    }

    @Override
    public void updateDisOrderStatusByDeviceIds(List<String> deviceIds, String status) {
        dao.updateDisOrderStatusByDeviceIds(deviceIds, status);
    }

    @Override
    public void batchSaveResourceDetail(List<ResourceDetailDTO> dtos) {
        for (ResourceDetailDTO dto : dtos) {
            dao.insert(convert.dto2do(dto));
        }
    }

    @Override
    public List<ResourceDetailDTO> list(ResourceDetailQuery query) {
        if (StringUtils.isBlank(query.getSourceType()) && CollectionUtils.isEmpty(query.getSourceTypeList())) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.STANDARD.getPrefix());
            sourceTypeList.add(SourceTypeEnum.NON_STANDARD.getPrefix());
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            sourceTypeList.add(SourceTypeEnum.XX.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public List<ResourceDetailDTO> listByDeviceIds(List<String> deviceIds) {
        if (CollectionUtil.isEmpty(deviceIds)) {
            return Collections.emptyList();
        }
        List<ResourceDetailDO> resourceDetailDOS = dao.list(new ResourceDetailQuery().setDeviceIds(deviceIds));
        return convert.doList2dtoList(resourceDetailDOS);
    }

    @Override
    public List<ResourceDetailDTO> listCorporate(ResourceDetailQuery query) {
        if (StringUtils.isBlank(query.getSourceType())) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    /**
     * 对公资源生成订单
     * @param query
     */
    @Override
    @Transactional
    public void generateCorporateOrder(ResourceDetailQuery query) {
        //查询资源数据
        query.setSourceType(null);
        List<String> sourceTypeList = new ArrayList<>();
        sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
        sourceTypeList.add(SourceTypeEnum.NON_STANDARD.getPrefix());
        query.setSourceTypeList(sourceTypeList);
        List<ResourceDetailDTO> detailDTOList = StreamUtils.mapArray(dao.list(query), convert::do2dto);
        //构造订单数据
//        List<CorporateOrderDTO> corporateOrderDTOList = new ArrayList<>();
//        List<CorporateOrderProductDTO> corporateOrderProductDTOList = new ArrayList<>();
        for (ResourceDetailDTO resourceDetailDTO : detailDTOList) {
            CorporateOrderDTO corporateOrderDTO = new CorporateOrderDTO();
            corporateOrderDTO.setId(OrderTypeEnum.CORPORATE.getPrefix() + '-' + IdUtil.nanoId());
            corporateOrderDTO.setOrderCode(OrderTypeEnum.CORPORATE.getPrefix() + "7" + System.currentTimeMillis());
            corporateOrderDTO.setCreateTime(resourceDetailDTO.getResourceApplyTime());
            corporateOrderDTO.setCreateBy(resourceDetailDTO.getApplyUserId());
            corporateOrderDTO.setCreateByName(resourceDetailDTO.getApplyUserName());
            corporateOrderDTO.setTenantId(resourceDetailDTO.getTenantId());
            corporateOrderDTO.setTenantName(resourceDetailDTO.getTenantName());
            corporateOrderDTO.setBusinessSystemId(resourceDetailDTO.getBusinessSysId());
            corporateOrderDTO.setBusinessSystemName(resourceDetailDTO.getBusinessSysName());
            corporateOrderDTO.setOrderAmount(BigDecimal.valueOf(1));
            corporateOrderManager.insert(corporateOrderDTO);
//            corporateOrderDTOList.add(corporateOrderDTO);
            CorporateOrderProductDTO corporateOrderProductDTO = new CorporateOrderProductDTO();
            if (ProductTypeEnum.ECS.getCode().equals(resourceDetailDTO.getType()) || ProductTypeEnum.GCS.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductEcs(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.EVS.getCode().equals(resourceDetailDTO.getType()) && StringUtils.isBlank(resourceDetailDTO.getVmId())) {
                corporateOrderProductDTO = generateCorporateProductEvs(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.OBS.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductObs(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.NAT.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductNat(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.SLB.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductSlb(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.EIP.getCode().equals(resourceDetailDTO.getType()) && StringUtils.isBlank(resourceDetailDTO.getVmId())) {
                corporateOrderProductDTO = generateCorporateProductEip(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.VPN.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductVpn(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.RDS_MYSQL.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductRdsMysql(resourceDetailDTO, corporateOrderDTO.getId());
            } else if (ProductTypeEnum.BACKUP.getCode().equals(resourceDetailDTO.getType())) {
                corporateOrderProductDTO = generateCorporateProductBackup(resourceDetailDTO, corporateOrderDTO.getId());
            }
//            corporateOrderProductDTOList.add(corporateOrderProductDTO);
            corporateOrderProductManager.insert(corporateOrderProductDTO);
            resourceDetailDTO.setOrderId(corporateOrderDTO.getId());
            resourceDetailDTO.setOrderCode(corporateOrderDTO.getOrderCode());
            this.updateById(resourceDetailDTO);
        }

    }

    private CorporateOrderProductDTO generateCorporateProductEcs(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        EcsModel model = new EcsModel();
        // 创建主产品（ESC）
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setVmName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        model.setAzId(Long.valueOf(resourceDetailDTO.getAzId()));
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setPlaneNetworkModel(JSON.parseArray(resourceDetailDTO.getNetworkModelSnapshot(), PlaneNetworkModel.class));
        model.setFlavorType("通用型");
        model.setFlavorName(resourceDetailDTO.getSpec());
        String osVersion = resourceDetailDTO.getOsVersion();
        if (Objects.nonNull(osVersion)) {
            OsInfo osInfo = parseOs(osVersion);
            model.setImageOs(osInfo.getName());
            model.setImageVersion(osInfo.getVersion());
        }
        String sysDisk = resourceDetailDTO.getSysDisk();
        if (Objects.nonNull(sysDisk)) {
            DiskInfo diskInfo = parseDisk(sysDisk);
            model.setSysDiskType(diskInfo.getSysDiskType());
            model.setSysDiskSize(Integer.valueOf(diskInfo.getSysDiskSize()));
        }
        String dataDisk = resourceDetailDTO.getDataDisk();
        List<EvsModel> evsModels = new ArrayList<>();
        if (Objects.nonNull(dataDisk)) {
            ArrayList<String> list = ListUtil.toList(dataDisk.split(","));
            for (String s : list) {
                EvsModel evsModel = new EvsModel();
                DiskInfo diskInfo = parseDisk(s);
                evsModel.setSysDiskType(diskInfo.getSysDiskType());
                evsModel.setSysDiskSize(Integer.valueOf(diskInfo.getSysDiskSize()));
                evsModels.add(evsModel);
            }
            model.setMountDataDisk(true);
            model.setMountDataDiskList(evsModels);
        }
        String bandWidth = resourceDetailDTO.getBandWidth();
        List<EipModel> eipModelList = new ArrayList<>();
        if (Objects.nonNull(bandWidth)) {
            EipModel eipModel = new EipModel();
            eipModel.setBandwidth(Integer.valueOf(bandWidth.replace("Mbps", "")));
            eipModel.setBindPublicIp(true);
            model.setBindPublicIp(true);
            eipModelList.add(eipModel);
            model.setEipModelList(eipModelList);
        }
        model.setUserName("root");
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    public static DiskInfo parseDisk(String diskString) {
        if (diskString == null || diskString.trim().isEmpty()) {
            return new DiskInfo("未知", "0");
        }

        // 正则表达式匹配：类型 + 空格 + 数字（可能后面跟单位）
        Pattern pattern = Pattern.compile("^([a-zA-Z]+)\\s+(\\d+)(GB|TB|MB)?$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(diskString.trim());

        if (matcher.find()) {
            String type = matcher.group(1).toUpperCase(); // SAS, SSD, HDD等
            String size = matcher.group(2); // 只取数字部分：40
            return new DiskInfo(type, size);
        }

        return fallbackParseDisk(diskString);
    }

    private static DiskInfo fallbackParseDisk(String diskString) {
        // 简单的空格分割方法，只提取数字
        String[] parts = diskString.trim().split("\\s+");
        if (parts.length >= 2) {
            String type = parts[0].toUpperCase();
            // 从第二部分提取纯数字
            String size = extractDigits(parts[1]);
            return new DiskInfo(type, size);
        }
        return new DiskInfo(diskString, "0");
    }

    private static String extractDigits(String input) {
        // 提取字符串中的所有数字
        return input.replaceAll("\\D+", "");
    }

    @Data
    public static class DiskInfo {
        private String sysDiskType;
        private String sysDiskSize;

        public DiskInfo(String sysDiskType, String sysDiskSize) {
            this.sysDiskType = sysDiskType;
            this.sysDiskSize = sysDiskSize;
        }

        public String getSysDiskType() { return sysDiskType; }
        public String getSysDiskSize() { return sysDiskSize; }

        @Override
        public String toString() {
            return "sysDiskType=" + sysDiskType + ", sysDiskSize=" + sysDiskSize;
        }
    }

    public OsInfo parseOs(String osString) {
        // 正则表达式匹配：名称 + 空格 + 版本（版本可能包含数字、点和空格）
        Pattern pattern = Pattern.compile("^([a-zA-Z\\s]+?)\\s+([\\d\\.]+\\s*.*)$");
        Matcher matcher = pattern.matcher(osString.trim());

        if (matcher.find()) {
            String name = matcher.group(1).trim();
            String version = matcher.group(2).trim();
            return new OsInfo(name, version);
        }

        // 如果正则匹配失败，尝试其他方法
        return fallbackParse(osString);
    }

    private static OsInfo fallbackParse(String osString) {
        // 简单的空格分割方法
        String[] parts = osString.trim().split("\\s+", 2);
        if (parts.length >= 2) {
            return new OsInfo(parts[0], parts[1]);
        } else {
            return new OsInfo(osString, "未知版本");
        }
    }

    // 操作系统信息类
    @Data
    public static class OsInfo {
        private String name;
        private String version;

        public OsInfo(String name, String version) {
            this.name = name;
            this.version = version;
        }

        public String getName() { return name; }
        public String getVersion() { return version; }

        @Override
        public String toString() {
            return "名称: " + name + ", 版本: " + version;
        }
    }

    private CorporateOrderProductDTO generateCorporateProductEvs(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        EvsModel model = new EvsModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setMountDataDisk(false);
        String dataDisk = resourceDetailDTO.getDataDisk();
        if (Objects.nonNull(dataDisk)) {
            DiskInfo diskInfo = parseDisk(dataDisk);
            model.setSysDiskType(diskInfo.getSysDiskType());
            model.setSysDiskSize(Integer.valueOf(diskInfo.getSysDiskSize()));
        }
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductObs(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        ObsModel model = new ObsModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setObsName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setStorageDiskType(resourceDetailDTO.getStoreType());
        model.setStorageDiskSize(Integer.valueOf(resourceDetailDTO.getCapacity().replace("GB", "")));
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductSlb(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        SlbModel model = new SlbModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setSlbName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setFlavorType("通用型");
        model.setFlavorName(resourceDetailDTO.getSpec());
        PlaneNetworkModel planeNetworkModel = JSON.parseObject(resourceDetailDTO.getNetworkModelSnapshot(), PlaneNetworkModel.class);
        model.setPlaneNetworkModel(planeNetworkModel);
        String bandWidth = resourceDetailDTO.getBandWidth();
        List<EipModel> eipModelList = new ArrayList<>();
        if (Objects.nonNull(bandWidth)) {
            EipModel eipModel = new EipModel();
            eipModel.setBandwidth(Integer.valueOf(bandWidth.replace("Mbps", "")));
            eipModel.setBindPublicIp(true);
            model.setBindPublicIp(true);
            eipModelList.add(eipModel);
            model.setEipModelList(eipModelList);
        }
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductNat(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        NatGatwayModel model = new NatGatwayModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setNatName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setFlavorType("通用型");
        model.setFlavorName(resourceDetailDTO.getSpec());
        PlaneNetworkModel planeNetworkModel = JSON.parseObject(resourceDetailDTO.getNetworkModelSnapshot(), PlaneNetworkModel.class);
        model.setPlaneNetworkModel(planeNetworkModel);
        String bandWidth = resourceDetailDTO.getBandWidth();
        List<EipModel> eipModelList = new ArrayList<>();
        if (Objects.nonNull(bandWidth)) {
            EipModel eipModel = new EipModel();
            eipModel.setBandwidth(Integer.valueOf(bandWidth.replace("Mbps", "")));
            eipModel.setBindPublicIp(true);
            model.setBindPublicIp(true);
            eipModelList.add(eipModel);
            model.setEipModelList(eipModelList);
        }
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductEip(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        EipModel model = new EipModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setEipName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setIpVersion(resourceDetailDTO.getIpVersion());
        model.setBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth().replace("Mbps", "")));
        model.setBindPublicIp(Objects.nonNull(resourceDetailDTO.getRelatedDeviceId()));
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductVpn(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        VpnModel model = new VpnModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setMaxConnection(Integer.valueOf(resourceDetailDTO.getSpec()));
        PlaneNetworkModel planeNetworkModel = new PlaneNetworkModel();
        planeNetworkModel.setId(resourceDetailDTO.getVpcId());
        planeNetworkModel.setName(resourceDetailDTO.getVpcName());
        PlaneNetworkModel.Subnet subnet = new PlaneNetworkModel.Subnet();
        subnet.setSubnetId(resourceDetailDTO.getSubnetId());
        subnet.setSubnetName(resourceDetailDTO.getSubnetName());
        planeNetworkModel.setSubnets(ListUtil.toList(subnet));
        model.setPlaneNetworkModel(planeNetworkModel);
        model.setBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth().replace("Mbps", "")));
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductBackup(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        BackupModel model = new BackupModel();
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setJobName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setBackupType(resourceDetailDTO.getBackupType());
        model.setFrequency(resourceDetailDTO.getFrequency());
        model.setDaysOfWeek(resourceDetailDTO.getDaysOfWeek());
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    private CorporateOrderProductDTO generateCorporateProductRdsMysql(ResourceDetailDTO resourceDetailDTO, String workOrderId) {
        Long mainId = IdUtil.getSnowflake().nextId();
        CorporateOrderProductDTO product = new CorporateOrderProductDTO();
        MysqlV2Model model = new MysqlV2Model();
        // 创建主产品（ESC）
        String productType = resourceDetailDTO.getType();
        model.setMainIds(ListUtil.toList(mainId));
        model.setId(mainId);
        //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
        model.setOriginName(resourceDetailDTO.getDeviceName());
        model.setMysqlName(resourceDetailDTO.getDeviceName());
        model.setAmount(BigDecimal.valueOf(1));
        model.setApplyTime(resourceDetailDTO.getApplyTime());
        model.setBillType("day");
        model.setDomainCode(resourceDetailDTO.getDomainCode());
        model.setDomainName(resourceDetailDTO.getDomainName());
        if (Objects.nonNull(resourceDetailDTO.getResourcePoolId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        if (Objects.nonNull(resourceDetailDTO.getAzId())) {
            model.setRegionId(Long.valueOf(resourceDetailDTO.getAzId()));
        }
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setPlaneNetworkModel(JSON.parseArray(resourceDetailDTO.getNetworkModelSnapshot(), PlaneNetworkModel.class));
        model.setFlavorType("通用型");
        model.setFlavorName(resourceDetailDTO.getSpec());
        model.setEngineVersion(resourceDetailDTO.getOsVersion());
        model.setDeployType(resourceDetailDTO.getMountOrNot());
        String sysDisk = resourceDetailDTO.getSysDisk();
        if (Objects.nonNull(sysDisk)) {
            DiskInfo diskInfo = parseDisk(sysDisk);
            model.setSysDiskType(diskInfo.getSysDiskType());
            model.setSysDiskSize(Integer.valueOf(diskInfo.getSysDiskSize()));
        }
        model.setTenantName(resourceDetailDTO.getTenantName());
        product.setId(mainId);
        product.setProductType(productType);
        product.setOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    @Override
    public PageResult<ResourceDetailDTO> page(ResourceDetailQuery query) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult();
        }
        String sourceType = query.getSourceType();
        if (StringUtils.isBlank(sourceType)) {
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.STANDARD.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        if ("DG".equals(sourceType)) {
            query.setSourceType(null);
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.CORPORATE.getPrefix());
            sourceTypeList.add(SourceTypeEnum.NON_STANDARD.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        } else if ("XX".equals(sourceType)) {
            query.setSourceType(null);
            List<String> sourceTypeList = new ArrayList<>();
            sourceTypeList.add(SourceTypeEnum.XX.getPrefix());
            query.setSourceTypeList(sourceTypeList);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ResourceDetailDO> list = dao.list(query);
        if ((ProductTypeEnum.ECS.getCode().equals(query.getType())
                || ProductTypeEnum.GCS.getCode().equals(query.getType())
                || ProductTypeEnum.MYSQL.getCode().equals(query.getType())
                || ProductTypeEnum.POSTGRESQL.getCode().equals(query.getType()))
                && CollectionUtil.isNotEmpty(list)) {
            convertSecurityGroupName(list);
            packageVnic(list, sourceType);
        }
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }

    @Override
    public PageResult<ResourceDetailDTO> pageEX(ResourceDetailQuery query) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult();
        }
        if (StringUtils.isBlank(query.getSourceType())) {
            query.setSourceType(SourceTypeEnum.EXTERNAL.getPrefix());
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ResourceDetailDO> list = dao.list(query);
        if (CollectionUtil.isNotEmpty(list)){
            for (ResourceDetailDO resourceDetailDO: list){
                resourceDetailDO.setId(resourceDetailDO.getGoodsOrderId());
            }
        }
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }

    private void convertSecurityGroupName(List<ResourceDetailDO> list) {
        // 1. 收集所有安全组ID
        List<Long> securityGroupIds = list.stream()
                .filter(r -> StringUtils.isNotBlank(r.getSecurityGroupIds()))
                .flatMap(r -> Arrays.stream(r.getSecurityGroupIds().split(",")))
                .map(Long::valueOf)
                .distinct()  // 去重，避免重复查询
                .collect(Collectors.toList());
        // 2. 批量查询安全组信息
        if (CollectionUtil.isEmpty(securityGroupIds)) return;
        Map<Long, String> idToNameMap = securityGroupDao.getByIds(securityGroupIds)
                .stream()
                .collect(Collectors.toMap(
                        WocSecurityGroupDO::getId,
                        WocSecurityGroupDO::getName
                ));
        // 3. 设置安全组名称
        list.forEach(detail -> {
            if (StringUtils.isNotBlank(detail.getSecurityGroupIds())) {
                String names = Arrays.stream(detail.getSecurityGroupIds().split(","))
                        .map(id -> idToNameMap.getOrDefault(Long.valueOf(id), ""))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                detail.setSecurityGroupName(names);
            }
        });
    }

    private void packageVnic(List<ResourceDetailDO> list, String sourceType) {
        List<String> deviceIds = list.stream().map(ResourceDetailDO::getDeviceId).collect(Collectors.toList());
        VnicQuery vnicQuery = new VnicQuery();
        vnicQuery.setVmIds(deviceIds);
        vnicQuery.setSourceType(sourceType);
        List<VnicDO> vnicDOList = vnicDAO.list(vnicQuery);
        if (CollectionUtil.isNotEmpty(vnicDOList)) {
            Map<String, List<VnicDO>> map = vnicDOList.stream().collect(Collectors.groupingBy(VnicDO::getVmId));
            for (ResourceDetailDO resourceDetailDO : list) {
                List<VnicDO> vnicDOS = map.get(resourceDetailDO.getDeviceId());
                if (CollectionUtil.isNotEmpty(vnicDOS)) {
                    resourceDetailDO.setVnicId(vnicDOS.stream().map(VnicDO::getId).collect(Collectors.joining(",")));
                    resourceDetailDO.setVnicName(vnicDOS.stream().map(VnicDO::getVnicName).collect(Collectors.joining(",")));
                }
            }
        }
    }

    @Override
    public ResourceDetailDTO getByGoodsOrderId(Long id) {
        return convert.do2dto(dao.getByGoodsOrderId(id));
    }

    @Override
    public List<ResourceDetailDTO> selectOrderGoodsByType(List<String> businessIds, List<String> productTypes, Integer recoveryType) {
        List<ResourceDetailDO> dos = dao.selectOrderGoodsByType(businessIds, productTypes, recoveryType);
        return convert.doList2dtoList(dos);
    }

    @Override
    public ResourceDetailDTO getByDeviceId(String deviceId) {
        return convert.do2dto(dao.getByDeviceId(deviceId));
    }

    @Override
    public void updateConfigId(String configId, Long id, String manageIp) {
        dao.updateConfigId(configId, id, manageIp);
    }

    /**
     * 根据实例uuid和ip查询网卡信息
     *
     * @param instanceUuid
     * @param ip
     * @return
     */
    @Override
    public NetcardDetailDTO selectNetInfoByUuidAndIp(String instanceUuid, String ip) {
        return dao.selectNetInfoByUuidAndIp(instanceUuid, ip);
    }

    /**
     * 操作云主机
     *
     * @param operate
     */
    @Override
    public CommonResult operateVm(VmOperateQuery operate) {
        //修改密码
        if (VmOperationEnum.RESETPWD.getCode().equals(operate.getOperationType())) {
            ResetVmPwdDTO dto = dao.selectOperateVmResetPwd(operate);
            HttpResult post = OkHttps.sync(layoutCenter + layoutVmResetPwd)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(dto))
                    .post();
            if (Objects.isNull(post)) {
                return CommonResult.failure("云主机修改密码失败");
            }
            Mapper dataMapper = post.getBody().toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "云主机修改密码失败，callLayoutOrder--编排中心初始化返回结果失败");
            return CommonResult.success("云主机修改密码成功");
        }
        //其他操作
        OperateVmDTO operateVmDTO = dao.selectOperateVm(operate);
        //重启操作需要校验是否处于开机状态
        if (VmOperationEnum.REBOOT.getAlias().equals(operate.getOperationType()) && !"RUNING".equals(operateVmDTO.getDeviceStatus())) {
            return CommonResult.failure("云主机需要开机才能重启");
        }
        if (VmOperationEnum.REBUILD_ECS.getAlias().equals(operate.getOperationType()) && "RUNING".equals(operateVmDTO.getDeviceStatus())) {
            return CommonResult.failure("云主机需要关机才能更换镜像");
        }
        if (VmOperationEnum.REBUILD_ECS.getAlias().equals(operate.getOperationType())) {
            if (StringUtils.isNotBlank(operateVmDTO.getVolumeId())) {
                return CommonResult.failure("云主机需要卸载数据盘才能更换镜像");
            }
            if (StringUtils.isNotBlank(operateVmDTO.getEip())) {
                return CommonResult.failure("云主机需要解绑弹性公网ip才能更换镜像");
            }
        }
        operateVmDTO.setImageId(operate.getImageId());
        HttpResult post = OkHttps.sync(layoutCenter + layoutVmOperate)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(operateVmDTO))
                .post();
        if (Objects.isNull(post)) {
            return CommonResult.failure("云主机操作失败");
        }
        Mapper dataMapper = post.getBody().toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "云主机操作失败，callLayoutOrder--编排中心初始化返回结果失败");
        //更新产品状态
        dao.updateDeviceStatus(operate.getOperationType() + "ING", operateVmDTO.getInstanceId());
        String desc = VmOperationEnum.getByAlias(operateVmDTO.getOperationType()).getDesc();
        return CommonResult.success(desc + "中，请稍后查看");
    }

    @Override
    public List<Long> selectBusinessIdByGoodsIds(List<String> goodsIds) {
        return dao.selectBusinessIdByGoodsIds(goodsIds);
    }

    @Override
    public List<ResourceDetailDTO> selectByGoodsIds(List<String> goodsIdList) {
        List<ResourceDetailDO> detailDOS = dao.selectByGoodsIds(goodsIdList);
        return convert.doList2dtoList(detailDOS);
    }

    @Override
    public ResourceDetailDTO getById(Long id) {
        ResourceDetailDO resourceDetailDO = dao.selectById(id);
        if (Objects.isNull(resourceDetailDO)) {
            return null;
        }
        List<ResourceDetailDO> list = ListUtil.toList(resourceDetailDO);
        convertSecurityGroupName(list);
        if ("BZ".equals(resourceDetailDO.getSourceType())) {
            resourceDetailDO.setSourceType(null);
        }
        packageVnic(list, resourceDetailDO.getSourceType());
        return convert.do2dto(list.get(0));
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }

    @Override
    public List<ResourceDetailDTO> selectRecoveryStatusByIds(List<String> ids, Integer recoveryStatus, Integer status) {
        List<ResourceDetailDO> detailDOS = dao.selectRecoveryStatusByIds(ids, recoveryStatus, status);
        return convert.doList2dtoList(detailDOS);
    }

    @Override
    public String selectByBusinessSystemId(Long businessSystemId) {
        return dao.selectByBusinessSystemId(businessSystemId);
    }

    @Override
    public List<String> selectRegionCodeByOrderId(String orderId) {
        return dao.selectRegionCodeByOrderId(orderId);}

    public List<ModuleOfflineDTO> selectNotUserModule(Long businessSysId) {
        return dao.selectNotUserModule(businessSysId);
    }

    @Override
    public ResourceDetailDTO getConfigId(String configId) {
        return convert.do2dto(dao.getConfigId(configId));
    }

    @Override
    public void updateChangeStatusByIds(List<Long> resourceIds, String changeStatus) {
        if (CollectionUtil.isNotEmpty(resourceIds)) {
            dao.updateChangeStatusByIds(resourceIds, changeStatus);
        }
    }

    @Override
    public void updateChangeStatusByDeviceIds(List<String> deviceIds, String changeStatus) {
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            dao.updateChangeStatusByDeviceIds(deviceIds, changeStatus);
        }
    }

    public void updateEipInfoByEipId(String eipId, String eip, String bandWidth) {
        dao.updateEipInfoByEipId(eipId, eip, bandWidth);
    }

    @Override
    public void evsAddEipInfoByDeviceId(String deviceId, String eipId, String eip, String bandWidth) {
        dao.evsAddEipInfoByDeviceId(deviceId, eipId, eip, bandWidth);
    }

    @Override
    public void clearEipInfoByEipId(String eipId) {
        dao.clearEipInfoByEipId(eipId);
    }

    @Override
    public void clearBackupInfoById(Long id) {
        dao.clearBackupInfoById(id);
    }

    @Override
    public void clearEipInfoById(Long id){
        dao.clearEipInfoById(id);
    }


    @Override
    public void clearRelatedInfoById(Long id) {
        dao.clearRelatedInfoById(id);
    }

    @Override
    public void updateEvsInfoById(ResourceDetailDTO dto) {
        dao.updateEvsInfoById(convert.dto2do(dto));
    }

    @Override
    public void updateHandoverStatusByConfigId(String configId, String handoverStatus) {
        dao.updateHandoverStatusByConfigId(configId, handoverStatus);
    }

    @Override
    public List<ResourceDetailDTO> selectByExpireTime(String time, String endTime) {
        List<ResourceDetailDO> detailDOS = dao.selectByExpireTime(time, endTime);
        return convert.doList2dtoList(detailDOS);
    }


    @Override
    public List<ResourceDetailDTO> selectByExpireTimeThreeDay() {
        List<ResourceDetailDO> detailDOS = dao.selectByExpireTimeThreeDay();
        return convert.doList2dtoList(detailDOS);
    }


    @Override
    public List<ResourceDetailDTO> selectExpireDetail() {
        List<ResourceDetailDO> detailDOS = dao.selectExpireDetail();
        return convert.doList2dtoList(detailDOS);
    }

    @Override
    public ResourceDetailDTO selectResourceDetailOfObs(Long id) {
        return dao.selectResourceDetailOfObs(id);
    }
}
