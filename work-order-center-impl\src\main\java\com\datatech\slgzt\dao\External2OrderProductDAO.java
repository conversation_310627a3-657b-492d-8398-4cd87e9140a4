package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.External2OrderProductMapper;
import com.datatech.slgzt.dao.model.order.External2OrderProductDO;
import com.datatech.slgzt.model.query.External2OrderProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class External2OrderProductDAO {

    @Resource
    private External2OrderProductMapper mapper;

    public List<External2OrderProductDO> list(External2OrderProductQuery query) {
        return mapper.selectList(
                Wrappers.<External2OrderProductDO>lambdaQuery()
                        .eq(ObjNullUtils.isNotNull(query.getOrderId()), External2OrderProductDO::getOrderId, query.getOrderId())
                        .in(ObjNullUtils.isNotNull(query.getIds()), External2OrderProductDO::getId, query.getIds())
                        .eq(ObjNullUtils.isNotNull(query.getProductType()), External2OrderProductDO::getProductType, query.getProductType())
                        .eq(ObjNullUtils.isNotNull(query.getParentId()), External2OrderProductDO::getParentProductId, query.getParentId())
                        .in(ObjNullUtils.isNotNull(query.getGids()), External2OrderProductDO::getGid, query.getGids())
                        .in(ObjNullUtils.isNotNull(query.getSubOrderIds()), External2OrderProductDO::getSubOrderId, query.getSubOrderIds())
                        .orderByDesc(External2OrderProductDO::getCreateTime)
        );
    }

    public void insert(External2OrderProductDO external2OrderProductDO) {
        mapper.insert(external2OrderProductDO);
    }

    public void updateById(External2OrderProductDO external2OrderProductDO) {
        mapper.updateById(external2OrderProductDO);
    }

    public void delete(Long id) {
        mapper.deleteById(id);
    }

    public void deleteByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<External2OrderProductDO>lambdaQuery()
                .eq(External2OrderProductDO::getOrderId, workOrderId));
    }

    public External2OrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    public External2OrderProductDO getByGid(String gid) {
        return mapper.selectOne(Wrappers.<External2OrderProductDO>lambdaQuery()
                .eq(External2OrderProductDO::getGid, gid));
    }

    public External2OrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<External2OrderProductDO>lambdaQuery()
                .eq(External2OrderProductDO::getSubOrderId, subOrderId));
    }

    public void updateByParentId(External2OrderProductDO external2OrderProductDO) {
        mapper.update(external2OrderProductDO, Wrappers.<External2OrderProductDO>lambdaUpdate()
                .eq(External2OrderProductDO::getParentProductId, external2OrderProductDO.getParentProductId()));
    }
}
