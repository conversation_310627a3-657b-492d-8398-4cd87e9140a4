package com.datatech.slgzt.impl.service.external2;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.convert.External2ChangeOrderServiceConvert;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.change.*;
import com.datatech.slgzt.model.dto.External2ChangeOrderDTO;
import com.datatech.slgzt.model.dto.External2ChangeOrderProductDTO;
import com.datatech.slgzt.model.dto.FlavorDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.opm.External2ChangeOrderCreateOpm;
import com.datatech.slgzt.model.query.External2ChangeOrderQuery;
import com.datatech.slgzt.model.query.FlavorQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.external2.External2ChangeOrderService;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 15:34:02
 */
@Service
@Slf4j
public class External2ChangeOrderServiceImpl implements External2ChangeOrderService {

    ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private UserService userService;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private External2ChangeOrderProductManager productManager;

    @Resource
    private External2ChangeOrderManager orderManager;

    @Resource
    private External2ChangeOrderServiceConvert convert;


    @Resource
    private FlavorModelManager flavorModelManager;

    @Resource
    private OrderDataProvideService orderDataProvideService;

    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;


    @Override
    public String createChangeWorkOrder(External2ChangeOrderCreateOpm opm) throws ExecutionException, InterruptedException {
        // 预先处理工单
        External2ChangeOrderDTO orderDTO = convert.convert(opm);
        orderDTO.setId(OrderTypeEnum.EXTERNAL2_CHANGE.getPrefix() + '-' + IdUtil.nanoId());
        orderDTO.setOrderCode(OrderTypeEnum.EXTERNAL2_CHANGE.getPrefix() + System.currentTimeMillis());
        orderDTO.setCreateTime(LocalDateTime.now());
        orderDTO.setCreatorId(opm.getCreatorId());
        orderDTO.setCreator(opm.getCreator());
        orderManager.add(orderDTO);
        //创建各自的部分
        String orderId = orderDTO.getId();
        //----------------ecs-------------------------------------------
        createEcs(opm, "ecs", orderId);
        //创建ecs
        //----------------gsc-------------------------------------------
        createEcs(opm, "gcs", orderId);
        //----------------mysql-------------------------------------------
        createEcs(opm, "mysql", orderId);
        //----------------postgreSql-------------------------------------------
        createEcs(opm, "postgreSql", orderId);
        //----------------redis-------------------------------------------
        createEcs(opm, "redis", orderId);
        // ----------------evs------------------------------------------
        createEvs(opm, ProductTypeEnum.EVS.getCode(), orderId);
        // ----------------共享evs------------------------------------------
        createEvs(opm, ProductTypeEnum.SHARE_EVS.getCode(), orderId);
        // ----------------slb------------------------------------------
        createSlb(opm, orderId);
        // ----------------nat------------------------------------------
        createNat(opm, orderId);
        // ----------------eip------------------------------------------
        createEip(opm, orderId);
        // ----------------obs------------------------------------------
        createObs(opm, orderId);
        // ----------------rdsMysql-------------------------------------
        createRdsMysql(opm, orderId);
        change(orderDTO);
        return orderId;
    }

    private void change(External2ChangeOrderDTO orderDTO) throws ExecutionException, InterruptedException {
        //第一次同步执行必然会失败的 主要是为了返回id
        //开启流程
        Long jobId = executor.submit(() -> {

            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("orderId", orderDTO.getId())
                    .addString("billId", orderDTO.getBillId())
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();
            //第一次同步执行必然会失败的 主要是为了返回id
            return jobLauncher.run(jobRegistry.getJob("external2ProductChangeJob"), jobParameters)
                    .getId();
        }).get();
        //存入jobId
        orderDTO.setJobExecutionId(jobId);
        orderManager.update(orderDTO);
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(jobId)
                .setRestartOnly(true)));
    }

    private void createEip(External2ChangeOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getEipPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(eip -> {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(eip.getResourceDetailId());
                //设置参数
                ChangeEipProductModel changeEipProductModel = new ChangeEipProductModel();
                changeEipProductModel.setResourceDetailId(eip.getResourceDetailId());
                changeEipProductModel.setEip(resourceDetailDTO.getEip());
                changeEipProductModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeEipProductModel.setEipId(resourceDetailDTO.getDeviceId());
                changeEipProductModel.setRelatedDeviceId(resourceDetailDTO.getRelatedDeviceId());
                changeEipProductModel.setRelatedDeviceName(resourceDetailDTO.getRelatedDeviceName());
                changeEipProductModel.setRelatedDeviceType(resourceDetailDTO.getRelatedDeviceType());
                changeEipProductModel.setEipName(resourceDetailDTO.getDeviceName());
                changeEipProductModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEipProductModel.setDomainName(resourceDetailDTO.getDomainName());
                changeEipProductModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEipProductModel.setChangeType(eip.getChangeType());
                //--------------------eip设置部分------------------------------------------------------------
                if (changeEipProductModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(eip.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth()
                                .substring(0, resourceDetailDTO.getBandWidth()
                                        .indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    //只有更变属性有的时候才会设置
                    eipModel.setChangeBandwidth(eip.getEipBandwidth());
                    changeEipProductModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeEipProductModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEipProductModel.setChangeTime(eip.getChangeTime());
                    changeEipProductModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeEipProductModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeEipProductModel, ProductTypeEnum.EIP.getCode(), orderId);
                lockDeviceIds.add(changeEipProductModel.getEipId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createObs(External2ChangeOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getObsPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(obs -> {
                //----------------------------------------obs设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(obs.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeObsModel changeObsModel = new ChangeObsModel();
                changeObsModel.setResourceDetailId(obs.getResourceDetailId());
                changeObsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeObsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeObsModel.setChangeType(obs.getChangeType());
                changeObsModel.setObsName(resourceDetailDTO.getDeviceName());
                changeObsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeObsModel.setDomainName(resourceDetailDTO.getDomainName());
                changeObsModel.setObsSpec(resourceDetailDTO.getSpec());
                //-----------------------------------扩容-----------------------------------
                if (changeObsModel.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    if (CollectionUtil.isNotEmpty(obs.getVolumeChangeReqModels())) {
                        changeObsModel.setChangeVolumeSize(obs.getVolumeChangeReqModels().get(0).getVolumeSize());
                    }
                }
                //-----------------------------------延期-----------------------------------
                if (changeObsModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeObsModel.setChangeTime(obs.getChangeTime());
                    changeObsModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeObsModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeObsModel, ProductTypeEnum.OBS.getCode(), orderId);
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createRdsMysql(External2ChangeOrderCreateOpm opm, String id) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getRdsMysqlPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(model -> {
                //----------------------------------------ecs/gcs设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(model.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeMysqlModel changeMysqlModel = new ChangeMysqlModel();
                changeMysqlModel.setSpec(resourceDetailDTO.getSpec());
                changeMysqlModel.setSysDisk(resourceDetailDTO.getSysDisk());
                changeMysqlModel.setResourceDetailId(model.getResourceDetailId());
                changeMysqlModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeMysqlModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeMysqlModel.setChangeType(model.getChangeType());
                changeMysqlModel.setDeviceId(resourceDetailDTO.getDeviceId());
                changeMysqlModel.setMysqlName(resourceDetailDTO.getDeviceName());
                changeMysqlModel.setEngineVersion(resourceDetailDTO.getOsVersion());
                changeMysqlModel.setDeployType(resourceDetailDTO.getMountOrNot());
                changeMysqlModel.setIp(resourceDetailDTO.getIp());
                changeMysqlModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeMysqlModel.setDomainName(resourceDetailDTO.getDomainName());
                //规格id在校验的时候被设置
                if (model.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    changeMysqlModel.setChangeFlavorName(model.getFlavorName());
                    changeMysqlModel.setChangeFlavorType(model.getFlavorType());
                    changeMysqlModel.setChangeFlavorId(model.getFlavorId());
                    changeMysqlModel.setSpec(resourceDetailDTO.getSpec());
                }
                //-------------------evs设置部分------------------------------------------------------------
                if (model.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    String sysDisk = resourceDetailDTO.getSysDisk();
                    List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = model.getVolumeChangeReqModels();
                    ChangeReqModel.VolumeChangeReqModel volumeChangeReqModel = volumeChangeReqModels.get(0);
                    List<ChangeEvsModel> changeEvsModels = Lists.newArrayList();
                    ChangeEvsModel changeEvsModel = new ChangeEvsModel();
                    changeEvsModel.setResourceDetailId(model.getResourceDetailId());
                    changeEvsModel.setChangeType(model.getChangeType());
                    changeEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    changeEvsModel.setSpec(sysDisk);
                    changeEvsModel.setChangeVolumeSize(volumeChangeReqModel.getVolumeSize());
                    changeEvsModel.setBillId(resourceDetailDTO.getBillId());
                    changeEvsModels.add(changeEvsModel);
                    changeMysqlModel.setEvsModelList(changeEvsModels);
                }
                commonFillParam(changeMysqlModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeMysqlModel, ProductTypeEnum.RDS_MYSQL.getCode(), id);
                lockDeviceIds.add(changeMysqlModel.getDeviceId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createNat(External2ChangeOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getNatPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(nat -> {
                //----------------------------------------nat设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(nat.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeNatModel changeNatModel = new ChangeNatModel();
                changeNatModel.setSpec(resourceDetailDTO.getSpec());
                changeNatModel.setEip(resourceDetailDTO.getEip());
                changeNatModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeNatModel.setNatName(resourceDetailDTO.getDeviceName());
                changeNatModel.setResourceDetailId(nat.getResourceDetailId());
                changeNatModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeNatModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeNatModel.setChangeType(nat.getChangeType());
                changeNatModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeNatModel.setDomainName(resourceDetailDTO.getDomainName());
                //-----------------------------------变更规格名称和类型-----------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    //规格id在校验的时候被设置
                    changeNatModel.setChangeFlavorName(nat.getFlavorName());
                    changeNatModel.setChangeFlavorType(nat.getFlavorType());
                    changeNatModel.setNatSpec(resourceDetailDTO.getSpec());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(nat.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth()
                                .substring(0, resourceDetailDTO.getBandWidth()
                                        .indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    eipModel.setResourceDetailId(eipDTO.getId());
                    changeNatModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeNatModel.setChangeTime(nat.getChangeTime());
                    changeNatModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeNatModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeNatModel, ProductTypeEnum.NAT.getCode(), orderId);
                lockDeviceIds.add(changeNatModel.getNatId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createSlb(External2ChangeOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getSlbPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(slb -> {
                //----------------------------------------slb设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(slb.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeSlbModel changeSlbModel = new ChangeSlbModel();
                changeSlbModel.setSpec(resourceDetailDTO.getSpec());
                changeSlbModel.setEip(resourceDetailDTO.getEip());
                changeSlbModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeSlbModel.setResourceDetailId(slb.getResourceDetailId());
                changeSlbModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeSlbModel.setSlbId(resourceDetailDTO.getDeviceId());
                changeSlbModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeSlbModel.setChangeType(slb.getChangeType());
                changeSlbModel.setSlbName(resourceDetailDTO.getDeviceName());
                changeSlbModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeSlbModel.setDomainName(resourceDetailDTO.getDomainName());
                //-----------------------------------变更规格名称和类型-----------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    //规格id在校验的时候被设置
                    changeSlbModel.setChangeFlavorName(slb.getFlavorName());
                    changeSlbModel.setChangeFlavorType(slb.getFlavorType());
                    changeSlbModel.setChangeFlavorId(slb.getFlavorId());
                    changeSlbModel.setSlbSpec(resourceDetailDTO.getSpec());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(slb.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth()
                                .substring(0, resourceDetailDTO.getBandWidth()
                                        .indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    eipModel.setChangeBandwidth(slb.getEipBandwidth());
                    eipModel.setChangeTime(slb.getChangeTime());
                    eipModel.setBillId(eipDTO.getBillId());
                    eipModel.setResourceDetailId(eipDTO.getId());
                    changeSlbModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeSlbModel.setChangeTime(slb.getChangeTime());
                    changeSlbModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeSlbModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeSlbModel, ProductTypeEnum.SLB.getCode(), orderId);
                lockDeviceIds.add(changeSlbModel.getSlbId());
                //如果eip创建
                if (slb.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    createEipProduct(Collections.singletonList(changeSlbModel.getEipModel()), changeSlbModel.getProductOrderId(), orderId);
                    lockDeviceIds.add(changeSlbModel.getEipModel().getEipId());
                }
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createProduct(ChangeBaseModel model, String productType, String workOrderId) {
        External2ChangeOrderProductDTO product = new External2ChangeOrderProductDTO();
        product.setId(model.getProductOrderId());
        product.setCreateWorkOrderId(model.getCreateWorkOrderId());
        product.setProductType(productType);
        product.setWorkOrderId(workOrderId);
        product.setChangeType(model.getChangeType());
        product.setParentProductId(0L);
        product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        product.setResourceDetailId(model.getResourceDetailId().toString());
        productManager.add(product);
    }

    private void createEvs(External2ChangeOrderCreateOpm opm, String productType, String id) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = null;
        if (productType.equals(ProductTypeEnum.EVS.getCode())) {
            changeReqModels = opm.getEvsPropertyList();
        } else if (productType.equals(ProductTypeEnum.SHARE_EVS.getCode())) {
            changeReqModels = opm.getShareEvsPropertyList();
        }
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(evs -> {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(evs.getResourceDetailId());
                //设置参数
                ChangeEvsModel changeEvsModel = new ChangeEvsModel();

                changeEvsModel.setResourceDetailId(evs.getResourceDetailId());
                changeEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEvsModel.setChangeType(evs.getChangeType());
                changeEvsModel.setEvsId(resourceDetailDTO.getDeviceId());
                changeEvsModel.setVmId(resourceDetailDTO.getVmId());
                changeEvsModel.setVmName(resourceDetailDTO.getEcsName());
                changeEvsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeEvsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEvsModel.setDomainName(resourceDetailDTO.getDomainName());
                changeEvsModel.setSpec(resourceDetailDTO.getDataDisk());
                if (evs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    changeEvsModel.setChangeVolumeSize(evs.getVolumeChangeReqModels().get(0).getVolumeSize());
                }
                if (evs.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEvsModel.setChangeTime(evs.getChangeTime());
                }
                commonFillParam(changeEvsModel, resourceDetailDTO, opm);
                //存入product表
                External2ChangeOrderProductDTO product = new External2ChangeOrderProductDTO();
                product.setId(changeEvsModel.getProductOrderId());
                product.setProductType(productType);
                product.setWorkOrderId(id);
                product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
                product.setChangeType(changeEvsModel.getChangeType());
                product.setPropertySnapshot(JSON.toJSONString(changeEvsModel));
                product.setSubOrderId(IdUtil.getSnowflake().nextId());
                product.setParentProductId(0L);
                product.setResourceDetailId(evs.getResourceDetailId().toString());
                productManager.add(product);
                lockDeviceIds.add(changeEvsModel.getEvsId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }


    private void createEcs(External2ChangeOrderCreateOpm opm, String productType, String id) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = Lists.newArrayList();
        if (productType.equals("ecs")) {
            changeReqModels = opm.getEcsPropertyList();
        }
        if (productType.equals("gcs")) {
            changeReqModels = opm.getGcsPropertyList();
        }
        if (productType.equals("mysql")) {
            changeReqModels = opm.getMysqlPropertyList();
        }
        if (productType.equals("postgreSql")) {
            changeReqModels = opm.getPostgreSqlPropertyList();
        }
        if (productType.equals("redis")) {
            changeReqModels = opm.getRedisPropertyList();
        }
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(model -> {
                //----------------------------------------ecs/gcs设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(model.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeEcsModel changeEcsModel = new ChangeEcsModel();
                changeEcsModel.setEip(resourceDetailDTO.getEip());
                changeEcsModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeEcsModel.setSpec(resourceDetailDTO.getSpec());
                changeEcsModel.setDataDisk(resourceDetailDTO.getDataDisk());
                changeEcsModel.setResourceDetailId(model.getResourceDetailId());
                changeEcsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEcsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeEcsModel.setChangeType(model.getChangeType());
                changeEcsModel.setVmId(resourceDetailDTO.getDeviceId());
                changeEcsModel.setVmName(resourceDetailDTO.getDeviceName());
                changeEcsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEcsModel.setDomainName(resourceDetailDTO.getDomainName());
                //规格id在校验的时候被设置
                if (model.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    changeEcsModel.setChangeFlavorName(model.getFlavorName());
                    changeEcsModel.setChangeFlavorType(model.getFlavorType());
                    changeEcsModel.setChangeFlavorId(model.getFlavorId());
                    changeEcsModel.setVmSpec(resourceDetailDTO.getSpec());
                    changeEcsModel.setTemplateCode(model.getTemplateCode());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (model.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel changeEipModel = new ChangeEipModel();
                    changeEipModel.setChangeType(model.getChangeType());
                    changeEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth()
                                .substring(0, resourceDetailDTO.getBandWidth()
                                        .indexOf("Mbps"));
                        changeEipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        changeEipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }

                    changeEipModel.setEipId(resourceDetailDTO.getEipId());
                    changeEipModel.setEip(resourceDetailDTO.getEip());
                    changeEipModel.setResourceDetailId(eipDTO.getId());
                    //只有更变属性有的时候才会设置
                    changeEipModel.setChangeBandwidth(model.getEipBandwidth());
                    changeEipModel.setBillId(eipDTO.getBillId());
                    changeEcsModel.setEipModel(changeEipModel);
                }
                //-------------------evs设置部分------------------------------------------------------------
                if (model.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    String volumeId = resourceDetailDTO.getVolumeId();
                    String dataDisk = resourceDetailDTO.getDataDisk();
                    //volumeId 和 dataDisk 都是逗号分隔的字符串,顺序是一样的，要保持和前端传过来的顺序一样
                    List<String> volumeIdList = Arrays.asList(volumeId.split(","));
                    List<String> dataDiskList = Arrays.asList(dataDisk.split(","));
                    // 创建一个映射关系以便快速查找
                    Map<String, String> volumeIdToDataDiskMap = new HashMap<>();
                    for (int i = 0; i < volumeIdList.size(); i++) {
                        // 假设两个列表长度相同且顺序对应
                        volumeIdToDataDiskMap.put(volumeIdList.get(i), dataDiskList.get(i));
                    }
                    List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = model.getVolumeChangeReqModels();
                    Map<String, ChangeReqModel.VolumeChangeReqModel> reqModelMap = StreamUtils.toMap(volumeChangeReqModels, ChangeReqModel.VolumeChangeReqModel::getId);
                    List<ChangeEvsModel> changeEvsModels = Lists.newArrayList();
                    volumeIdList.forEach(volume -> {
                        ChangeReqModel.VolumeChangeReqModel volumeChangeReqModel = reqModelMap.get(volume);
                        if (volumeChangeReqModel == null) {
                            return;
                        }
                        ResourceDetailDTO evsDTO = resourceDetailManager.getByDeviceId(volume);
                        Precondition.checkArgument(evsDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", volume));
                        ChangeEvsModel changeEvsModel = new ChangeEvsModel();
                        changeEvsModel.setResourceDetailId(evsDTO.getId());
                        changeEvsModel.setChangeType(model.getChangeType());
                        changeEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        changeEvsModel.setEvsId(volume);
                        changeEvsModel.setSpec(volumeIdToDataDiskMap.get(volume));
                        changeEvsModel.setChangeVolumeSize(reqModelMap.getOrDefault(volume, new ChangeReqModel.VolumeChangeReqModel())
                                .getVolumeSize());
                        changeEvsModel.setBillId(resourceDetailDTO.getBillId());
                        changeEvsModels.add(changeEvsModel);
                    });
                    changeEcsModel.setEvsModelList(changeEvsModels);
                }
                //如果包含延期
                if (model.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEcsModel.setChangeTime(model.getChangeTime());
                }
                commonFillParam(changeEcsModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeEcsModel, productType, id);
                lockDeviceIds.add(changeEcsModel.getVmId());
                //如果evs创建
                if (model.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    createEvsProduct(changeEcsModel.getEvsModelList(), changeEcsModel.getProductOrderId(), id);
                    //整理出所有的EVSID
                    lockDeviceIds.addAll(StreamUtils.mapArray(changeEcsModel.getEvsModelList(), ChangeEvsModel::getEvsId));
                }
                //如果eip创建
                if (model.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    createEipProduct(Collections.singletonList(changeEcsModel.getEipModel()), changeEcsModel.getProductOrderId(), id);
                    lockDeviceIds.add(changeEcsModel.getEipModel().getEipId());
                }
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createEvsProduct(List<ChangeEvsModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;

        models.stream().map(model -> {
            External2ChangeOrderProductDTO product = new External2ChangeOrderProductDTO();
            product.setResourceDetailId(model.getResourceDetailId().toString());
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
            product.setChangeType(model.getChangeType());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            return product;
        }).forEach(productManager::add);
    }

    private void createEipProduct(List<ChangeEipModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;
        models.stream().map(model -> {
            External2ChangeOrderProductDTO product = new External2ChangeOrderProductDTO();
            product.setResourceDetailId(model.getResourceDetailId().toString());
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
            product.setChangeType(model.getChangeType());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setParentProductId(parentId);
            return product;
        }).forEach(productManager::add);
    }

    private void commonFillParam(ChangeBaseModel model, ResourceDetailDTO resourceDetailDTO, External2ChangeOrderCreateOpm opm) {
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionId(resourceDetailDTO.getResourcePoolId());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzId(resourceDetailDTO.getAzId());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setBillId(resourceDetailDTO.getBillId());
        model.setCustomNo(opm.getCustomNo());
        model.setTenantId(opm.getTenantId());
        model.setTenantName(opm.getTenantName());
        model.setExpireTime(resourceDetailDTO.getExpireTime());
        model.setResourceId(resourceDetailDTO.getDeviceId());
    }

    @Override
    public PageResult<External2ChangeOrderDTO> page(External2ChangeOrderQuery query, Long userId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult<>();
        }
        return orderManager.page(query);
    }


    @Override
    public void checkChangeOrderCanCreate(External2ChangeOrderCreateOpm opm) {
        List<Long> evsResourceIds = CollectionUtil.isNotEmpty(opm.getEvsPropertyList()) ?
                opm.getEvsPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //eipId
        List<Long> eipResourceIds = CollectionUtil.isNotEmpty(opm.getEipPropertyList()) ?
                opm.getEipPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //ecsId
        List<Long> ecsResourceIds = CollectionUtil.isNotEmpty(opm.getEcsPropertyList()) ?
                opm.getEcsPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //mysqlId
        List<Long> mysqlResourceIds = CollectionUtil.isNotEmpty(opm.getMysqlPropertyList()) ?
                opm.getMysqlPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //redisId
        List<Long> redisResourceIds = CollectionUtil.isNotEmpty(opm.getRedisPropertyList()) ?
                opm.getRedisPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //gcsId
        List<Long> gcsResourceIds = CollectionUtil.isNotEmpty(opm.getGcsPropertyList()) ?
                opm.getGcsPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //slbId
        List<Long> slbResourceIds = CollectionUtil.isNotEmpty(opm.getSlbPropertyList()) ?
                opm.getSlbPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //obsId
        List<Long> obsResourceIds = CollectionUtil.isNotEmpty(opm.getObsPropertyList()) ?
                opm.getObsPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //natId
        List<Long> natResourceIds = CollectionUtil.isNotEmpty(opm.getNatPropertyList()) ?
                opm.getNatPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        //rdsMysqlId
        List<Long> rdsMysqlResourceIds = CollectionUtil.isNotEmpty(opm.getRdsMysqlPropertyList()) ?
                opm.getRdsMysqlPropertyList().stream()
                        .map(ChangeReqModel::getResourceDetailId)
                        .distinct()
                        .collect(Collectors.toList())
                : Lists.newArrayList();
        List<Long> mergeList = StreamUtils.mergeList(evsResourceIds, eipResourceIds, ecsResourceIds, gcsResourceIds, slbResourceIds, obsResourceIds, natResourceIds, mysqlResourceIds, redisResourceIds, rdsMysqlResourceIds);
        Precondition.checkArgument(CollectionUtil.isNotEmpty(mergeList), "变更时资源id不能为空");
        if (StringUtils.isEmpty(opm.getId())) {
            checkUnRecoveryOrUnChange(mergeList);
        }
        //----------------------ecs校验----------------------------
        checkFillEcs(opm, evsResourceIds, eipResourceIds);
        //----------------------gcs校验------------------------------
        checkFillGcs(opm, evsResourceIds, eipResourceIds);
        //----------------------mysql校验------------------------------
        checkFillMysql(opm, evsResourceIds, eipResourceIds);
        //----------------------redis校验------------------------------
        checkFillRedis(opm, evsResourceIds, eipResourceIds);
        //----------------------evs校验------------------------------
        checkFillEvs(opm);
        //----------------------slb校验------------------------------
        checkFillSlb(opm, eipResourceIds);
        //----------------------Eip校验------------------------------
        checkFillEip(opm);
        //----------------------rdsMysql校验-------------------------
        checkFillRdsMysql(opm);

    }

    @Override
    public void tryStartEcs(ResourceDetailDTO detailDTO) {
        if (detailDTO != null && "STOPED".equals(detailDTO.getDeviceStatus())
                && (ProductTypeEnum.ECS.getCode().equals(detailDTO.getType())
                || ProductTypeEnum.GCS.getCode().equals(detailDTO.getType())
                || ProductTypeEnum.REDIS.getCode().equals(detailDTO.getType())
                || ProductTypeEnum.MYSQL.getCode().equals(detailDTO.getType())
                || ProductTypeEnum.POSTGRESQL.getCode().equals(detailDTO.getType()))) {
            try {
                VmOperateQuery operate = new VmOperateQuery();
                operate.setId(detailDTO.getId());
                operate.setOperationType(VmOperationEnum.START.getAlias());
                operate.setOrderId(detailDTO.getOrderId());
                resourceDetailManager.operateVm(operate);
            } catch (Exception e) {
                log.warn("开启失败，detailDTO:{}, msg:{}", detailDTO, ExceptionUtils.getStackTrace(e));
            }
        }
    }

    @Override
    public void restart(String workOrderId) {
        External2ChangeOrderDTO orderDTO = orderManager.getById(workOrderId);
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, workOrderId, KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(orderDTO.getJobExecutionId())
                .setRestartOnly(true)));
    }

    private void checkFillRdsMysql(External2ChangeOrderCreateOpm opm) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> rdsMysqlPropertyList = opm.getRdsMysqlPropertyList();
        if (CollectionUtil.isEmpty(rdsMysqlPropertyList)) {
            return;
        }
        rdsMysqlPropertyList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setType("RDS")
                        .setDescription(detailDTO.getMountOrNot())
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                //List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                query.setPageSize(1000).setPageNum(1);
                PageResult<FlavorDTO> flavorDTOPageResult = flavorModelManager.pageCorporate(query);
                List<FlavorDTO> flavorDTOS = flavorDTOPageResult.getRecords();
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeSize：%s在请求中不能为空", evs.getVolumeSize()));
                });
            }
        });
    }


    private void checkFillEip(External2ChangeOrderCreateOpm opm) {
        //获取Eip对象
        List<ChangeReqModel> eipList = opm.getEipPropertyList();
        if (CollectionUtil.isEmpty(eipList)) {
            return;
        }
        eipList.forEach(eip -> {
            ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(eip.getResourceDetailId());
            fillCheckTenantId(opm, resourceDetailDTO);
            //判断是否包含bandwidth_change
            if (eip.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //校验带宽不能为空
                Precondition.checkArgument(eip.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eip.getEipId()));
            }
        });
    }

    private void checkFillSlb(External2ChangeOrderCreateOpm opm, List<Long> eipResourceIds) {
        //获取Slb对象
        List<ChangeReqModel> slbList = opm.getSlbPropertyList();
        if (CollectionUtil.isEmpty(slbList)) {
            return;
        }
        slbList.forEach(slb -> {
            ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(slb.getResourceDetailId());
            fillCheckTenantId(opm, resourceDetailDTO);
            //判断是否包含storage_expand
            if (slb.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                fillCheckTenantId(opm, resourceDetailDTO);
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(slb.getFlavorName())
                        .setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", slb.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", slb.getFlavorName()));
                //设置规格id
                slb.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含bandwidth_change
            if (slb.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = slb.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(slb.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验是否存在
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                //校验是否存在
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在SLB中已经存在", eipId));
            }
        });
    }


    private void checkFillEvs(External2ChangeOrderCreateOpm opm) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> evsList = opm.getEvsPropertyList();
        if (CollectionUtil.isEmpty(evsList)) {
            return;
        }
        evsList.forEach(evs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(evs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断是否包含storage_expand
            if (evs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = evs.getVolumeChangeReqModels();
                volumeChangeReqModels.forEach(i -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getResourceDetailId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getResourceDetailId()));
                    //校验大小不能为空
                    Precondition.checkArgument(i.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getResourceDetailId()));
                });
            }
        });
    }

    private void checkFillEcs(External2ChangeOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> ecsList = opm.getEcsPropertyList();
        if (CollectionUtil.isEmpty(ecsList)) {
            return;
        }
        ecsList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setType("VM")
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }

    private void checkFillGcs(External2ChangeOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> gcsList = opm.getGcsPropertyList();
        if (CollectionUtil.isEmpty(gcsList)) {
            return;
        }
        gcsList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setType("GPU")
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }

    private void checkFillMysql(External2ChangeOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> mysqlList = opm.getMysqlPropertyList();
        if (CollectionUtil.isEmpty(mysqlList)) {
            return;
        }
        mysqlList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setType("VM")
                        .setName(ecs.getFlavorName())
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }

    private void checkFillRedis(External2ChangeOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> redisList = opm.getRedisPropertyList();
        if (CollectionUtil.isEmpty(redisList)) {
            return;
        }
        redisList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            fillCheckTenantId(opm, detailDTO);
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setType("VM")
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }


    private void checkUnRecoveryOrUnChange(List<Long> resourceIds) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setIds(resourceIds);
        List<ResourceDetailDTO> detailDTOS = resourceDetailManager.list(query);
        // 排除回收中的资源
        long unRecoveryCount = detailDTOS.stream()
                .filter(item -> RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType()
                        .equals(item.getRecoveryStatus()))
                .count();
        Precondition.checkArgument(unRecoveryCount == resourceIds.size(), "变更产品中传入的资源id中存在回收中的资源，该次变更失败，请确认");
        // 排除变更中的资源
        long canChangeCount = detailDTOS.stream()
                .filter(item -> ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType()
                        .equals(item.getChangeStatus())
                        || ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode()
                        .equals(item.getChangeStatus()))
                .count();
        Precondition.checkArgument(canChangeCount == resourceIds.size(), "变更产品中传入的资源id中存在变更中的资源，该次变更失败，请确认");
//        detailDTOS.forEach(resourceDetailDTO -> {
//            boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
//            Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
//        });
    }


    private static void fillCheckTenantId(External2ChangeOrderCreateOpm opm, ResourceDetailDTO resourceDetailDTO) {
        if (opm.getTenantId() == null) {
            opm.setTenantId(resourceDetailDTO.getTenantId());
            opm.setTenantName(resourceDetailDTO.getTenantName());
        } else {
            Precondition.checkArgument(opm.getTenantId().equals(resourceDetailDTO.getTenantId()),
                    "每个退订订单中的资源需同属一个租户");
        }
    }

}
